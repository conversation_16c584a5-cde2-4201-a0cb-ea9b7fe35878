<?php declare(strict_types = 1);

namespace App\Console\PickupPoint;

use App\Console\BaseCommand;
use App\Model\ConfigService;
use App\Model\Orm\DeliveryMethod\PPLPickup;
use App\Model\Orm\Orm;
use GuzzleHttp\Client;
use Nette\Utils\DateTime;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Model\Orm\State\State;

#[AsCommand(
	name: 'pickupPoint:import:pplpickup',
	description: 'PPL Pickup branch import',
)]
// [AsCronTask(expression: '# 22 * * *', transports: 'cronCommands')]
final class ImportPPLPickupCommand extends BaseCommand
{

	private const ENDPOINT = 'https://api.dhl.com/ecs/ppl/myapi2/accessPoint';
	private const ENDPOINT_AUTH = 'https://api.dhl.com/ecs/ppl/myapi2/login/getAccessToken';

	private const array ENDPOINT_COUNTRY_CODES = [State::CODE_CZ, State::CODE_SK];

	private const DELIVERY_METHOD_IDENT = PPLPickup::ID;

	private const WEEKDAYS = [
		2 => 'Pondělí',
		3 => 'Úterý',
		4 => 'Středa',
		5 => 'Čtvrtek',
		6 => 'Pátek',
		7 => 'Sobota',
		1 => 'Neděle',
	];

	private array $credentials;

	public function __construct(
		private readonly Connection $connection,
		protected readonly Orm $orm,
		protected readonly ConfigService $configService,
	)
	{
		parent::__construct();
		$this->credentials = $this->configService->get('pickupPoints', 'ppl');
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		if (($deliveryMethod = $this->orm->deliveryMethod->getBy(['deliveryMethodUniqueIdentifier' => self::DELIVERY_METHOD_IDENT])) === null) {
			$output->writeln('Delivery method with unique identifier ' . self::DELIVERY_METHOD_IDENT . ' not exists. Skipping import.');
			return self::FAILURE;
		}

		$output->writeln('Delivery method: ' . $deliveryMethod->name);

		try {
			$client = new Client();
			$options = [];
			$options['headers']['Authorization'] = 'Bearer ' . $this->authenticate();

			$limit = 1000;
			$break = null;

			$i = 0;
			$imported = [];
			foreach (self::ENDPOINT_COUNTRY_CODES as $countryCode) {

				$state = $this->orm->state->getBy(['code' => $countryCode]);
				$output->writeln('CountryCode: ' . $countryCode . ' / ' . $state->name);

				$y = 0;
				do {
					$offset                          = $y * $limit;
					$options['query']['Limit']       = $limit;
					$options['query']['Offset']      = $offset;
					$options['query']['CountryCode'] = $countryCode;
					$options['query']['pickupEnabled'] = 'true'; // ASP.NET boolean type

					$request    = $client->get(self::ENDPOINT, $options);
					$totalCount = (int) ($request->getHeader('X-Paging-Total-Items-Count')[0] ?? 0);
					$response   = Json::decode($request->getBody()->getContents(), forceArrays: true);

					foreach ($response as $item) {

						$name = $item['name'];
						$extId = $item['accessPointCode'];
						$address = $item['street'] . ', ' . $item['zipCode'] . ' ' . $item['city'];

						$data = [
							'deliveryMethodId' => $deliveryMethod->id,
							'extId' => $extId,
							'name' => $name,
							'address' => $address,
							'syncTime' => new DateTimeImmutable(),
							'lat' => $item['gps']['latitude'],
							'lng' => $item['gps']['longitude'],
							'openingHours' => Json::encode($this->getOpeningHours($item)),
							'stateId' => $state->id,
						];

						$imported[] = $extId;

						$this->connection->query('INSERT INTO [pickup_point] %values ON DUPLICATE KEY UPDATE %set', $data, $data);
						$i++;
					}

					$y++;
					if ($break !== null && $y === $break) { // @phpstan-ignore-line
						break;
					}
				} while (count($response) > 0 && ($offset + $limit) <= $totalCount);
			}

			if ($imported !== []) {
				$this->connection->query('DELETE FROM [pickup_point] WHERE deliveryMethodId = %?i AND extId NOT IN %s[]', $deliveryMethod->id, $imported);
			}

			$output->writeln('Total processed: ' . $i);

		} catch (\Throwable $e) {
			$output->writeln($e->getMessage());
		}

		return $this->end(self::SUCCESS);
	}

	private function getOpeningHours(array $item): array
	{
		$content = [];

		foreach ($item['workHours'] as $workHour) {
			$content[self::WEEKDAYS[$workHour['weekDay']]][] = ['from' => substr($workHour['openFrom'], 0, -3), 'to' => substr($workHour['openTo'], 0, -3)];
		}

		return $content;
	}

	private function authenticate(): string
	{
		$dir = TEMP_DIR . '/import';
		$file = '/ppl_oauth_credentials.json';

		if (file_exists($dir . $file)) {
			try {
				$response = Json::decode(FileSystem::read($dir . $file), forceArrays: true);

				$datetime = DateTime::from($response['datetime']);
				if ((new DateTime())->getTimestamp() < ($datetime->getTimestamp() + $response['expires_in'])) {
					return $response['access_token'];
				}
			} catch (\Throwable) {
				// do nothing
			}
		}

		$client = new Client();

		$options = ['form_params' => ['grant_type' => 'client_credentials', 'scope' => 'myapi2'] + $this->credentials];
		$request = $client->request('POST', self::ENDPOINT_AUTH, $options);
		$response = Json::decode($request->getBody()->getContents(), forceArrays: true);

		$response['datetime'] = new DateTime();

		FileSystem::createDir($dir);
		FileSystem::write($dir . $file, Json::encode($response));

		return $response['access_token'];
	}

}
