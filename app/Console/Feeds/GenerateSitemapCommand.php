<?php declare(strict_types = 1);

namespace App\Console\Feeds;

use App\Console\BaseCommand;
use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Sitemap\SitemapGenerator;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'feeds:generate:sitemap',
	description: 'Generate sitemaps',
)]
class GenerateSitemapCommand extends BaseCommand
{

	public function __construct(
		private readonly MutationRepository $mutationRepository,
		protected readonly ConfigService $configService,
		private readonly SitemapGenerator $sitemapGenerator,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('mutationCode', InputArgument::OPTIONAL, 'code fo language mutation', Mutation::CODE_CS);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->setIsLockable(true, $this->configService->get('env'));
		$this->start($input);
		$output->writeLn('START');

		$mutationCode = $input->getArgument('mutationCode');
		$createdFiles = $this->sitemapGenerator->generate($this->mutationRepository->getByChecked(['langCode' => $mutationCode]));
		foreach ($createdFiles as $createdFile) {
			$output->writeln($createdFile);
		}
		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
