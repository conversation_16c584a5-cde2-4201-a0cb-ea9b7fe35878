<?php

declare(strict_types=1);

namespace App\Model\Router;

use App\Model\ConfigService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\SeoLinkModel;
use function array_key_exists;
use function count;
use function is_array;

final class FilterSeoLink
{

	private Tree|null $eshopPage = null;

	public function __construct(
		private readonly SeoLinkModel $seoLinkModel,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
		private readonly MutationHolder $mutationHolder,
	)
	{
	}

	public function in(array $params): array
	{
		if ( ! isset($params['object']) || ! $params['object'] instanceof SeoLinkLocalization) {
			return $params;
		}

		$seoLinkLocalization = $params['object'];

		$eshopPage = $this->getEshopPage($seoLinkLocalization->mutation);
		$params['object'] = $eshopPage;
		$params['mutation'] = $seoLinkLocalization->mutation;
		$params['seoLink'] = $seoLinkLocalization;
		$params['presenter'] = 'Front:Catalog';
		$params['action'] = 'default';

		if (isset($params['filter']) && ! is_array($params['filter'])) {
			$params['filter'] = [];
		}

		$parameterValues = [];
		foreach ($seoLinkLocalization->seoLink->parameterValues as $parameterValue) {
			if ( ! array_key_exists($parameterValue->parameter->uid, $parameterValues)) {
				$parameterValues[$parameterValue->parameter->uid] = [];
			}

			$parameterValues[$parameterValue->parameter->uid][$parameterValue->id] = $parameterValue->id;
		}

		$possibleParams = $this->configService->get('seoLink', 'paramsList');
		foreach ($possibleParams as $paramUid) {
			if (array_key_exists($paramUid, $parameterValues)) {
				$params['filter']['dials'][$paramUid] = $parameterValues[$paramUid];
			}
		}

		return $params;
	}

	public function out(array $params): array
	{
		if (isset($params['seoLink'])) {
			unset($params['seoLink']);
		}
		if ( ! ($params['presenter'] === 'Front:Catalog' && $params['action'] === 'default')) {
			return $params;
		}

		if (isset($params['filter']) && is_array($params['filter']) && count($params['filter']) > 0) {

			if (isset($params['mutation'])) {
				$mutation = $params['mutation'];
			} else {
				$mutation = $this->mutationHolder->getMutation();
			}

			$eshopPage = $this->getEshopPage($mutation);

			if (isset($params['filter']['order'])) {
				$params['order'] = $params['filter']['order'];
			}

			if (isset($params['object']) && $params['object'] === $eshopPage) {
				$mutation = $params['mutation'] ?? $this->mutationHolder->getMutation();
				$seoLink = $this->seoLinkModel->getByFilter($mutation, $params['filter'], $params);

				if ($seoLink !== null && $seoLink->alias !== null) {
					$params['alias'] = $seoLink->alias->alias;
					unset($params['filter']);
				} else {
					$params['alias'] = $eshopPage->alias->alias;
				}
			}
		}

		return $params;
	}

	private function getEshopPage(Mutation $mutation): Tree
	{
		return $this->eshopPage ??= $this->orm->tree->getByUid('eshop', $mutation); // TODO constant?
	}

}
