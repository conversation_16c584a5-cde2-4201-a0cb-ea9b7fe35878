<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Product;

use App\Model\ElasticSearch\All\Convertor\ProductData;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use Closure;
use Elastica\Query\BoolQuery;
use Elastica\QueryBuilder;
use Generator;

class ProductIdsFinder
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		private readonly Repository $esProductRepository,
	)
	{
	}

	/**
	 * @return Closure(): Generator
	 */
	public function getIteratorProductIds(Mutation $mutation, BoolQuery $query): Closure
	{
		$index = $this->esIndexRepository->getProductLastActive($mutation);
		if ($index === null) {
			return fn() => yield from [];
		}

		return $this->esProductRepository->getFinderClosure($index, $query);
	}

	/**
	 * @return Closure(): Generator
	 */
	public function getIteratorProductIdsInAll(Mutation $mutation, BoolQuery $query): Closure
	{
		$b = new QueryBuilder();
		$query->addMust(
			$b->query()->term(['type' => ProductData::KIND_PRODUCT])
		);

		$index = $this->esIndexRepository->getAllLastActive($mutation);
		if ($index === null) {
			return fn() => yield from [];
		}

		return $this->esProductRepository->getFinderClosure($index, $query);
	}

}
