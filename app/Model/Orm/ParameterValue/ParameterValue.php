<?php

declare(strict_types=1);

namespace App\Model\Orm\ParameterValue;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasTranslator;
use App\Model\TranslatorDB;
use App\PostType\Discount\Model\Orm\Discount\Discount;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer; // phpcs:ignore

/**
 * @property int $id {primary}
 * @property int $sort {default 0}
 * @property int $parameterSort {default 0}
 * @property bool $prioritySort {default false}
 * @property bool $isHidden {default false}
 * @property string $internalValue
 * @property string $internalAlias {default ''}
 * @property string|null $extId
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<ProductVariant> $variants1  {1:m ProductVariant::$param1Value}
 * @property OneHasMany<ProductVariant> $variants2  {1:m ProductVariant::$param2Value}
 * @property ManyHasMany<Product> $products {m:m Product::$parametersValues}
 * @property Parameter $parameter  {m:1 Parameter::$options}
 * @property Discount|null $discount  {1:1 Discount::$parameterValueDiscount}
 *
 * VIRTUALS
 * @property ArrayHash|null $cf {virtual}
 * @property-read string $value {virtual}
 * @property-read string $alias {virtual}
 * @property-read string|null $filterValue {virtual}
 * @property-read string|null $filterValueTitle {virtual}
 * @property-read bool $isLanguageParameter {virtual}
 * @property-read bool $isCzechLanguage {virtual}
 * @property-read bool $hasDetail {virtual}
 */
class ParameterValue extends Entity
{

	use HasTranslator;
	use HasCustomFields;

	public const ALIAS_LANGUAGE_CZECH = 'cesky';
	public const TRANS_TYPE_VALUE = 'value';
	public const TRANS_TYPE_ALIAS = 'alias';
	public const TRANS_TYPE_FILTER = 'filter';

	public const EXT_ID_AUDIO_BOOK = 'audiokniha';
	public const ALIAS_ONLINE = 'online';


	protected function getterFirstImage(): never
	{
		throw new \Exception('TODO: implement first image for parameter?');
	}


	protected function getterValue(): string
	{
		if ($this->isPersisted() && $this->parameter->hasTranslatedValues === true) {
			$defaultValue = 'pvalue_' . $this->id;
			$translation = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);
			return ($translation === $defaultValue) ? $this->internalValue : $translation;
		}

		return $this->internalValue;
	}


	protected function getterAlias(): string
	{
		if ($this->isPersisted() && $this->parameter->hasTranslatedValues === true) {
			$defaultValue = 'pvalue_alias_' . $this->id;
			$translation = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);

			return ($translation === $defaultValue) ? $this->internalAlias : $translation;
		}

		return $this->internalAlias;
	}


	protected function getterFilterValue(): ?string
	{
		if ($this->isPersisted() && $this->parameter->hasTranslatedValues === true) {
			$defaultValue = 'pvalue_filter_' . $this->id;
			$translation = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);

			return ($translation === $defaultValue) ? null : $translation;
		}

		return null;
	}
	protected function getterFilterValueTitle(): ?string
	{
		if ($this->isPersisted() && $this->parameter->hasTranslatedValues === true) {
			$defaultValue = 'pvalue_filter_title_' . $this->id;
			$translation = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);

			return ($translation === $defaultValue) ? null : $translation;
		}

		return null;
	}


	protected function getterHasDetail(): bool
	{
		return match ($this->parameter->uid) {
			default => false,
		};
	}

	protected function getterIsLanguageParameter(): bool
	{
		return $this->parameter->uid === Parameter::UID_LANGUAGE;
	}

	protected function getterIsCzechLanguage(): bool
	{
		return $this->internalAlias === self::ALIAS_LANGUAGE_CZECH;
	}

	public function getColorClass(): string
	{
		if (isset($this->cf->courseType->color)) {
			return $this->cf->courseType->color;
		}
		return '';
	}


	public function getTooltip(Mutation $mutation): string
	{
		$tooltipName = 'tooltip_' . $mutation->langCode;

		if (isset($this->cf->courseType->$tooltipName)) {
			return $this->cf->courseType->$tooltipName;
		}
		return '';
	}

}
