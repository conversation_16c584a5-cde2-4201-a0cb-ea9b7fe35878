<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

use DateTimeImmutable;

/**
 * @property string|null $variableSymbol
 * @property DateTimeImmutable|null $dueDate
 */
final class LegacyPaymentInformation extends PaymentInformation
{

	protected function getType(): PaymentType
	{
		return PaymentType::LegacyPayment;
	}

	protected function getInitialState(): PaymentState
	{
		return PaymentState::Pending;
	}

	public function isOnline(): bool
	{
		return false;
	}

	public function redirectUrl(): ?string
	{
		return null;
	}

	public function getState(): PaymentState
	{
		return $this->state;
	}

}
