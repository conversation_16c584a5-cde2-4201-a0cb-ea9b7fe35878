<?php declare(strict_types = 1);

namespace App\Model\Orm\UserMutation;

use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasSimpleSave;

/**
 * @method UserMutation|null getById($id)
 * @method UserMutation|null getBy(array $conds)
 * @method UserMutation save(?UserMutation $entity, array $data)
 *
 * @extends Repository<UserMutation>
 */
final class UserMutationRepository extends Repository
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [UserMutation::class];
	}

}
