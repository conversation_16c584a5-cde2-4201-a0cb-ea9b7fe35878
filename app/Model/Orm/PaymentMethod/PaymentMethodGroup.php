<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\TranslatorDB;

final class PaymentMethodGroup
{

	private const DATA = [
		BenefitCard::GROUP => [
			'name' => 'payment_method_group_benefit_card_title',
			'desc' => 'payment_method_group_benefit_card_desc',
			'tooltip' => 'payment_method_group_benefit_card_tooltip',
			'isRecommended' => false,
			'isGift' => false,
			'imgs' => [],
		],
	];

	public string $name;

	public ?string $desc;

	public ?string $tooltip;

	public bool $isRecommended = false;

	public bool $isGift = false;

	public array $imgs = [];

	public function __construct(
		private readonly string $groupId,
		private readonly TranslatorDB $translator,
	)
	{
		foreach (self::DATA[$this->groupId] as $key => $value) {
			$this->{$key} = is_string($value) ? $this->translator->translate($value) : $value;
		}
	}

	public function getId(): string
	{
		return $this->groupId;
	}

}
