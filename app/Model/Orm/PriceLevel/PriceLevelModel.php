<?php declare(strict_types = 1);

namespace App\Model\Orm\PriceLevel;

use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasStaticCache;
use Nextras\Orm\Collection\ICollection;

class PriceLevelModel
{

	use HasStaticCache;


	public function __construct(
		private readonly PriceLevelRepository $repository
	)
	{
	}


	public function getAllPriceLevel(): array
	{
		return $this->tryLoadCache($this->createCacheKey('getAllPriceLevel'), function () {
			return $this->repository->findAll()->fetchAll();
		});
	}

	public function getAllPriceLevelByType(array $onlyTypes = []): array
	{
		return $this->tryLoadCache($this->createCacheKey('getAllPriceLevelByType', $onlyTypes), function () use ($onlyTypes) {
			if ($onlyTypes !== []) {
				return $this->repository->findBy(['type' => $onlyTypes])->fetchPairs('type');
			}
			return $this->repository->findAll()->fetchPairs('type');
		});
	}

	public function getById(int $priceLevelId): ?PriceLevel
	{
		return $this->findAll()[$priceLevelId] ?? null;
	}

	public function findAll(): array
	{
		return $this->tryLoadCache($this->createCacheKey('findAll'), function () {
			return $this->repository->findAll()->fetchPairs('id');
		});
	}

}
