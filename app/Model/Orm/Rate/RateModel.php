<?php
declare(strict_types = 1);

namespace App\Model\Orm\Rate;


use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Traits\HasCache;

final class RateModel
{
	use HasCache;
	private ?array $rates = null;

	public function __construct(
		private readonly RateRepository $rateRepository,
		private readonly MutationsHolder $mutationsHolder,
	) {
	}

	public function getAmount(float $amount, string $currency, float $delta = 0.25, ?float $addMargin = 0.05): float
	{
		if($this->mutationsHolder->getDefault()->currency->getCurrencyCode() === $currency){
			return $amount;
		}

		$price         = $this->calculateAmount($amount, $this->getLastExchangeRate($currency), $addMargin);
		$previousPrice = $this->calculateAmount($amount, $this->getPreviousExchangeRate($currency), $addMargin);

		if (($previousPrice - $delta) > $price || ($previousPrice + $delta) < $price) {
			return $price;
		}

		return $previousPrice;
	}

	public function calculateAmount(float $amount, float $exchangeRate, ?float $addMargin = null): float
	{
		$price = $amount / $exchangeRate;
		if ($addMargin !== null) {
			$price += ($price * $addMargin); // +
		}
		return $price;
	}

	public function getLastExchangeRate(string $currency): float
	{
		return $this->loadCache($this->createCacheKey('lastExchangeRate', $currency), function () use ($currency) {
			if ($this->rates === null) {
				$this->rates[$currency] = $this->rateRepository->findLastTwo($currency);
			}

			return $this->rates[$currency][0] ?? Rate::DEFAULT_RATES[$currency];
		});
	}

	public function getPreviousExchangeRate(string $currency): float
	{
		return $this->loadCache($this->createCacheKey('previousExchangeRate', $currency), function () use ($currency) {
			if ($this->rates === null) {
				$this->rates[$currency] = $this->rateRepository->findLastTwo($currency);
			}

			return $this->rates[$currency][1] ?? $this->getLastExchangeRate($currency);
		});
	}

	public function hasRateChanged(string $currency): bool
	{
		return $this->getLastExchangeRate($currency) !== $this->getPreviousExchangeRate($currency);
	}
}
