<?php

declare(strict_types = 1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method DeliveryMethodConfiguration|null getFreeDelivery(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency)
 * @method Money|null getFreeDeliveryAmount(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency)
 * @method ICollection<DeliveryMethodConfiguration> getAvailable(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency)
 * @method DeliveryMethodConfiguration|null getPrefferedDelivery(User $user)
 * @method bool hasFreeTransportsForProduct(Mutation $mutation, PriceLevel $priceLevel, State $state, Money $price, float $weight)
 * @extends Repository<DeliveryMethodConfiguration>
 */
final class DeliveryMethodConfigurationRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [DeliveryMethodConfiguration::class];
	}

	public function getTransitFreeFromLevel(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency): int|null
	{
		if (!$freeDelivery = $this->getFreeDelivery($mutation, $state, $priceLevel, $currency)) {
			return null;
		}

		$transitFreeFrom = null;

		foreach ($freeDelivery->prices as $price) {

			if ($price->freeFrom === null) {
				continue;
			}

			if ($transitFreeFrom === null) {
				$transitFreeFrom = $price->freeFrom->toInt();
				continue;
			}

			if ($price->freeFrom->toInt() <= $transitFreeFrom) {
				$transitFreeFrom = $price->freeFrom->toInt();
			}
		}

		return $transitFreeFrom;
	}

	public function getFreeDeliveriesLimits(): array
	{
		$freeDeliveries = $this->findBy(['calculateFree' => true]);

		if (!$freeDeliveries->count()) {
			return [];
		}

		$freeDeliveriesLimit = [];

		foreach ($freeDeliveries as $freeDelivery) {

			$freeDeliveriesLimit[$freeDelivery->getPersistedId()] = [
				'maxWeight' => $this->inspectDeliveryMaxWeight($freeDelivery->prices->toCollection()),
				//TODO dimensions, etc...
			];
		}

		return $freeDeliveriesLimit;
	}

	/**
	 * @param ICollection<DeliveryMethodPrice> $deliveryPrices
	 */
	private function inspectDeliveryMaxWeight(ICollection $deliveryPrices): int|null
	{
		if (!$deliveryPrices->count()) {
			return null;
		}

		$deliveryWeighLimit = null;

		/**
		 * @var DeliveryMethodPrice $price
		 */
		foreach ($deliveryPrices as $price) {

			if ($price->maxWeight === null) {
				continue;
			}

			if ($deliveryWeighLimit === null) {
				$deliveryWeighLimit = $price->maxWeight;
			}

			if ($deliveryWeighLimit < $price->maxWeight) {
				$deliveryWeighLimit = $price->maxWeight;
			}
		}

		return $deliveryWeighLimit;
	}

}
