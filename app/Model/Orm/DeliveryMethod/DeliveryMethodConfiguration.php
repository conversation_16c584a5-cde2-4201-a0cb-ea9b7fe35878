<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Exceptions\LogicException;
use App\Model\DeliveryDate;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Delivery\OrderDelivery;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateModel;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\VatCalculator;
use App\PostType\Core\Model\Publishable;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use stdClass;
use App\Model\Orm\JsonContainer; // phpcs:ignore


/**
 * @property-read int $id {primary}
 * @property ?string $externalId {default null}
 *
 * @property string $name {default ''}
 * @property string $desc {default ''}
 * @property ?string $pageText {default null}
 * @property string|null $tooltip {default null}
 * @property bool $public {default false}
 *
 * @property int $sort {default 0}
 * @property bool $pageShow {default false}
 * @property bool $isRecommended {default false}
 * @property bool $isGift {default false}
 * @property bool $calculateFree {default false}
 *
 * @property int $deliveryDayFrom {default 0}
 * @property int|null $deliveryDayTo {default null}
 * @property stdClass $deliveryHourByStock {container JsonContainer}
 *
 * @property string $deliveryMethodUniqueIdentifier
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property-read ManyHasMany<State> $countries {m:m State, isMain=true, oneSided=true}
 * @property-read ManyHasMany<PaymentMethodConfiguration> $allowedPaymentMethods {m:m PaymentMethodConfiguration, isMain=true, oneSided=true}
 *
 * @property-read OneHasMany<DeliveryMethodPrice> $prices {1:m DeliveryMethodPrice::$deliveryMethod}
 * @property-read OneHasMany<OrderDelivery> $orderItems {1:m OrderDelivery::$deliveryMethod}
 * @property-read OneHasMany<DeliveryMethodCurrency> $currencies {1:m DeliveryMethodCurrency::$deliveryMethod}
 * @property array $vats {container JsonContainer}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash|null $cf {virtual}
 */
final class DeliveryMethodConfiguration extends BaseEntity implements Publishable
{

	use HasCustomFields;

	public string $uid = 'deliveryMethod';

	private DeliveryMethodRegistry $deliveryMethodRegistry;

	private StateModel $stateModel;

	private OrderProxy $shoppingCart;

	public function injectServices(
		DeliveryMethodRegistry $registry,
		StateModel $stateModel,
		ShoppingCartInterface $shoppingCart,
	): void
	{
		$this->deliveryMethodRegistry = $registry;
		$this->stateModel = $stateModel;
		$this->shoppingCart = $shoppingCart;
	}

	public function getDeliveryMethod(): DeliveryMethod
	{
		return $this->deliveryMethodRegistry->get($this->deliveryMethodUniqueIdentifier);
	}

	public function price(PriceLevel $priceLevel, State $country, OrderProxy $order): ?Money
	{
		$price = $this->getPrice($priceLevel, $country, $order->getCurrency());

		if ($price !== null) {
			$orderProductPrice = $order->getTotalProductPriceVat();

			if ($this->shoppingCart->getUserEntity()?->freeTransit) {
				return Money::of(0, $order->getCurrency());
			}

			if ($order->hasItemWithForcedFreeDelivery() && $this->calculateFree) {
				return Money::of(0, $order->getCurrency());
			}

			if ($price->freeFrom !== null && $orderProductPrice->isGreaterThanOrEqualTo($price->freeFrom)) {
				return Money::of(0, $order->getCurrency());
			}

			return VatCalculator::priceWithoutVat($price->price->asMoney(), $this->vatRate($country));
		}

		return null;
	}

	public function priceVat(PriceLevel $priceLevel, State $country, OrderProxy $order): ?Money
	{
		$price = $this->price($priceLevel, $country, $order);
		if ($price === null) {
			return null;
		}

		return VatCalculator::priceWithVat($price, $this->vatRate($country));
	}

	public function getPrice(PriceLevel $priceLevel, State $country, Currency $currency): ?DeliveryMethodPrice
	{
		return $this->prices->toCollection()->getBy([
			'priceLevel' => $priceLevel,
			'state' => $country,
			'price->currency' => $currency->getCurrencyCode(),
		]);
	}


	public function getVatRate(?State $country = null): VatRate
	{
		$countryId = $country?->id ?? State::DEFAULT_ID;
		return isset($this->vats->$countryId) ? VatRate::from($this->vats->$countryId) : VatRate::Standard;
	}

	public function getDateDeliveryFrom(): ?DateTimeImmutable
	{
		$dateDelivery = $this->shoppingCart->getWorstDeliveryDate($this);

		if ( ! $dateDelivery instanceof DeliveryDate /*|| $dateDelivery->from === null*/) {
			return null;
		}

		return new DateTimeImmutable($dateDelivery->from->format('Y-m-d'));
	}

	public function getDateDeliveryTo(): ?DateTimeImmutable
	{
		$dateDelivery = $this->shoppingCart->getWorstDeliveryDate($this);

		if ( ! $dateDelivery instanceof DeliveryDate || $dateDelivery->to === null) {
			return null;
		}

		return new DateTimeImmutable($dateDelivery->to->format('Y-m-d'));
	}

	public function getDateDeliveryExpedition(): ?DateTimeImmutable
	{
		$dateDelivery = $this->shoppingCart->getWorstDeliveryDate($this);

		if ( ! $dateDelivery instanceof DeliveryDate || $dateDelivery->expedition === null) {
			return null;
		}

		return new DateTimeImmutable($dateDelivery->expedition->format('Y-m-d'));
	}

	public function isTransitFree(PriceLevel $priceLevel, State $state, Currency $currency): bool
	{
		if ($this->calculateFree === false) {
			return false;
		}

		if ($this->shoppingCart->getTotalPriceVat()->isZero()) {
			return false;
		}

		$price = $this->getPrice($priceLevel, $state, $currency);

		if ($price === null) {
			return false;
		}

		if (!$freeFrom = $price->freeFrom) {
			return false;
		}

		return $freeFrom->minus($this->shoppingCart->getTotalPriceVat()->getAmount())->isNegativeOrZero();
	}

	public function isAllowed(PriceLevel $priceLevel, State $country, Currency $currency, BigDecimal $weight): bool
	{
		return $this->loadCache($this->createCacheKey('isAllowed', $priceLevel, $country, $weight, $currency), function () use ($priceLevel, $country, $weight, $currency) {
			$price = $this->getPrice($priceLevel, $country, $currency);
			if ($price === null) {
				return false;
			}

			return $price->maxWeight === null || !$weight->isGreaterThan($price->maxWeight);
		});
	}

	public function getMaxCodPrice(PriceLevel $priceLevel, State $country, ShoppingCartInterface $shoppingCart): ?Money
	{
		return $this->loadCache($this->createCacheKey('maxCodPrice', $priceLevel, $country, $shoppingCart), function () use ($priceLevel, $country, $shoppingCart) {
			$price = $this->getPrice($priceLevel, $country, $shoppingCart->getCurrency());
			if ($price !== null && $price->maxCodPrice !== null) {
				return Price::from(Money::of($price->maxCodPrice, $shoppingCart->getCurrency()))->asMoney();
			}
			return null;
		});
	}

	public function vatRate(?State $state = null): BigDecimal
	{
		$vatRateType = $this->getVatRate($state);

		$stateId = $state ? $state->id : State::DEFAULT_ID;
		$vatRates = $this->stateModel->getAllVatRatesValues($this->mutation);
		$rate = $vatRates[$stateId]->get($vatRateType);

		if ($rate === null) {
			throw new LogicException('Unknown vatRate.');// nekonzistentni stav, ktery musime odhalit, nelze pouzit defaultni sazbu, prodavalo by se spatne a neprislo by se na to -> je to tak?
		}

		return $rate;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

}
