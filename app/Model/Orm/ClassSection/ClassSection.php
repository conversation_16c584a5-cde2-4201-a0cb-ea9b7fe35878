<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassSection;

use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassEventSectionMetadata\ClassEventSectionMetadata;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasConsts;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string|null $extId {default null}
 * @property string $name {default ''}
 * @property string $type {default self::TYPE_CATEGORY}
 * @property int $completionTime {default 0}
 * @property int $sort {default 0}
 * @property string $content {default ''}
 * @property string $description {default ''}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$classSections}
 * @property Product $product {m:1 Product::$classSections}
 * @property OneHasMany<ClassEventSectionMetadata> $classEventSectionMetadata {1:m ClassEventSectionMetadata::$classSection}
 * VIRTUAL
 */
class ClassSection extends Entity
{

	use HasConsts;

	public const TYPE_CATEGORY = 'category';
	public const TYPE_BREAK = 'pause';
	public const TYPE_ON_SITE = 'onsite';
	public const TYPE_INFOGRAPHIC = 'infographic';
	public const TYPE_ARTICLE = 'article';
	public const TYPE_DOCUMENT = 'document';
	public const TYPE_VIDEO = 'video';
	public const TYPE_QUIZ = 'quiz';
	public const TYPE_CERTIFICATION = 'certification';
	public const TYPE_ONLINE_MEETING = 'online_meeting';
	public const TYPE_ONLINE = 'online';

	public function getRealDescription(?ClassEvent $classEvent = null): string
	{
		if ($classEvent !== null) {
			$classEventSectionMetadata = $this->classEventSectionMetadata->toCollection()->getBy([
				'classEvent' => $classEvent,
			]);
			if ($classEventSectionMetadata !== null) {
				return $classEventSectionMetadata->description;
			}
		}

		return $this->description;
	}


	public function getRealContent(?ClassEvent $classEvent = null): string
	{
		if ($classEvent !== null) {
			$classEventSectionMetadata = $this->classEventSectionMetadata->toCollection()->getBy([
				'classEvent' => $classEvent,
			]);
			if ($classEventSectionMetadata !== null) {
				return $classEventSectionMetadata->content;
			}
		}

		return $this->content;
	}

}
