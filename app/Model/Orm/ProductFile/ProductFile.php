<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductFile;

use App\Model\Orm\File\File;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string|null $name
 * @property string|null $url
 * @property string|null $size
 * @property int|null $sort
 *
 *
 * RELATIONS
 * @property ProductLocalization $productLocalization {m:1 ProductLocalization::$files}
 * @property File $file {m:1 File::$productFiles}
 *
 *
 * VIRTUALS
 * @property string $ext {virtual}
 * @property string $filename {virtual}
 */
class ProductFile extends Entity
{

	public function getterFilename(): string
	{
		if (isset($this->url)) {
			$lastPos = strrpos($this->url, '/');
			$fromPos = $lastPos === false ? 0 : $lastPos + 1;
			return substr($this->url, $fromPos);
		} else {
			return '';
		}
	}

	protected function getterExt(): string
	{
		return isset($this->url) ? pathinfo($this->url, PATHINFO_EXTENSION) : '';
	}

}
