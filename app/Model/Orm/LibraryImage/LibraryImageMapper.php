<?php declare(strict_types = 1);

namespace App\Model\Orm\LibraryImage;

use App\Model\Orm\LibraryTree\LibraryTree;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<LibraryImage>
 */
final class LibraryImageMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'image';
}
	/**
	 * @return ICollection<LibraryImage>
	 */
	public function findByFilter(array $filterData, ?LibraryTree $libraryTree): ICollection
	{
		$builder = $this->builder();

		if (isset($filterData['fulltext'])) {
			$builder->andWhere('( name LIKE %_like_ or alts LIKE %_like_)', $filterData['fulltext'], $filterData['fulltext']);
		}

		if (isset($filterData['directorySearch']) && $filterData['directorySearch']) {
			if ($libraryTree !== null) {
				$builder->andWhere('libraryId = %i', $libraryTree->id);
			}
		}

		return $this->toCollection($builder);
	}
	/**
	 * @return ICollection<LibraryImage>
	 */
	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()->select('i.*')->from('image', 'i')
			->andWhere('i.id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(i.id, ' . implode(',', $ids) . ')');
		return $this->toCollection($builder);
	}


	public function fixSortForDirectory(LibraryTree $libraryTree): void
	{
		$images = $this->builder()->select('i.*')->from($this->getTableName(), 'i')
			->andWhere('i.libraryId = %i', $libraryTree->id)
			->addOrderBy('sort asc');

		$results = $this->connection->queryByQueryBuilder($images);
		$sort = 10;
		foreach ($results as $image) {
			$this->connection->query('UPDATE image set sort = %i where id = %i', $sort, $image->id);
			$sort = $sort + 10;
		}
	}


	public function findImagesForSitemap(int $limit, int $offset): Result
	{
		return $this->connection->query('SELECT i.filename, i.timeOfChange, i.timeOfChange FROM %table as i
			LEFT JOIN product_image as pi on (i.id = pi.libraryImageId)
		    LEFT JOIN product as p on (p.id = pi.productId)
		    LEFT JOIN product_localization as pl on (p.id = pl.productId)
			WHERE pi.id IS NOT null
			  AND i.filename IS NOT null
			  AND pl.public = 1
			ORDER BY i.id
			LIMIT %i OFFSET %i;', $this->getTableName(), $limit, $offset);
	}


}
