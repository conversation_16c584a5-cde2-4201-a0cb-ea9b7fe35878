<?php declare(strict_types = 1);

namespace App\Model\Orm\MyLibrary;

use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\User\User;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends <PERSON>balMapper<MyLibrary>
 */
final class MyLibraryMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'my_library';
}

	public function findUserProductIds(User $userEntity): array
	{
		$query = $this->builder()->select('mlp.productId as productId')
			->from('my_library_product', 'mlp')
			->joinLeft('[my_library] AS [ml]', '[ml.id] = [mlp.myLibraryId]')
			->andWhere('ml.userId = %i', $userEntity->id);

		return $this->connection->queryByQueryBuilder($query)->fetchPairs(null, 'productId');
	}

}
