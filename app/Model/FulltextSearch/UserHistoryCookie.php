<?php declare(strict_types=1);

namespace App\Model\FulltextSearch;

use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Utils\Json;
use Throwable;

class UserHistoryCookie
{

	private array $list;

	private const COOKIE_EXPIRE = '+3 month';
	public const COOKIE_NAME = 'last_searched';

	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
	)
	{
	}

	public function get(): array
	{
		if (!isset($this->list)) {
			$this->list = $this->loadCookie();
		}
		return $this->list;
	}

	public function add(string $text): void
	{
		$list = $this->get();
		array_unshift($list, $text);
		$this->list = array_unique($list);

		$this->save();
	}


	private function loadCookie(): array
	{
		try {
			$value = $this->httpRequest->getCookie(self::COOKIE_NAME);
			if ($value !== null) {
				return Json::decode($value, forceArrays: true);
			}
		} catch (Throwable) {
			//bad data in json -> ignore
		}
		return [];
	}


	private function save(): void
	{
		$this->httpResponse->setCookie(self::COOKIE_NAME, Json::encode($this->list), self::COOKIE_EXPIRE);
	}

}
