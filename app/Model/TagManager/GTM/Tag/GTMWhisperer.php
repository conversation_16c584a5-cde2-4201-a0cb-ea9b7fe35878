<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\SearchClick;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMWhisperer implements RenderableTag
{

	public function __construct(
		private SearchClick $searchView
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'whisperer';
	}

	public function getData(): array
	{
		return [
			'event' => $this->getEventName(),
			'whisperer_action' => $this->searchView->action,
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return $this->searchView->action === 'view' ? 'suggest' : null;
	}

}
