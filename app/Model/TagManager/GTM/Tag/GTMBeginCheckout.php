<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\BeginCheckout;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMBeginCheckout implements RenderableTag
{

	public function __construct(
		private BeginCheckout $beginCheckout
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'begin_checkout';
	}

	public function getData(): array
	{
		$items = [];

		$mutation = $this->beginCheckout->shoppingCart->getMutation();
		$currency = $this->beginCheckout->shoppingCart->getCurrency();

		$i = 0;
		/** @var ProductItem $productItem */
		foreach ($this->beginCheckout->shoppingCart->getProducts() as $productItem) {
			$items[] = $productItem->variant->product->getLocalization($mutation)->getGTMData(
				variant: $productItem->variant,
				currency: $currency,
				index: $i,
				quantity: $productItem->amount,
			);
			$i++;
		}

		$ecommerce = [
			'currency' => $this->beginCheckout->shoppingCart->getCurrency()->getCurrencyCode(),
			'value' => $this->beginCheckout->shoppingCart->getTotalPrice()->getAmount()->toFloat(),
			'items' => $items,
		];

		$coupon = $this->beginCheckout->shoppingCart->getAppliedVoucherItem();
		if ($coupon !== null) {
			$ecommerce['coupon'] = $coupon->voucherCode->code;
		}

		return [
			'event' => $this->getEventName(),
			'ecommerce' => $ecommerce,
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
