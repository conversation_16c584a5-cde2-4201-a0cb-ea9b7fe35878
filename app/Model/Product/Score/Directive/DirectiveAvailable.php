<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;

final class DirectiveAvailable extends Directive implements DirectiveInterface
{

	final public function process(): float
	{
		return match ($this->getProductLocalization()->product->productAvailability->getType()) {
			CustomProductAvailability::TYPE_ON_STOCK => 3.0,
			CustomProductAvailability::TYPE_PREORDER, CustomProductAvailability::TYPE_TO_ORDER => 10,
			CustomProductAvailability::TYPE_ON_STOCK_SUPPLIER => 0,
			CustomProductAvailability::TYPE_OUT_OF_STOCK, CustomProductAvailability::TYPE_NOT_FOR_SALE => -50,
			default => 0.0,
		};
	}

}
