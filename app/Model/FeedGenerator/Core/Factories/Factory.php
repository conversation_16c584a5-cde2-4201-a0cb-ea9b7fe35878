<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\Factories;

use App\Model\FeedGenerator\Core\Exceptions\DtoFactoryException;
use App\Model\FeedGenerator\Core\Exceptions\ProviderFactoryException;

class Factory implements FactoryInterface
{

	private string|null $mutationCode = null;

	public function __construct(
		private readonly DispatcherFactory $dispatcherFactory,
		private readonly GeneratorFactory $generatorFactory,
	)
	{
	}

	public function dispatcherFactory(): DispatcherFactoryInterface
	{
		$this->dispatcherFactory->setFactory($this);

		return $this->dispatcherFactory;
	}

	public function dtoFactory(): DtoFactoryInterface
	{
		throw new DtoFactoryException(DtoFactoryException::NOT_IMPLEMENTED);
	}

	public function generatorFactory(): GeneratorFactoryInterface
	{
		$this->generatorFactory->setFactory($this);

		return $this->generatorFactory;
	}

	public function providerFactory(): ProviderFactoryInterface
	{
		throw new ProviderFactoryException(ProviderFactoryException::NOT_IMPLEMENTED);
	}

	final public function setMutationCode(string $mutationCode): FactoryInterface
	{
		$this->mutationCode = $mutationCode;
		return $this;
	}

	final public function getMutationCode(): string|null
	{
		return $this->mutationCode;
	}

}
