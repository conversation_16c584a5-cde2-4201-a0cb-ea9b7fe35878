<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Product\Consumer;

use App\Model\Messenger\Elasticsearch\Product\Message\QuickReplaceProductMessage;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

#[AsMessageHandler]
class QuickReplaceProductConsumer extends BaseReplaceProductConsumer
{

	public function __invoke(QuickReplaceProductMessage $message): void
	{
		$this->consume($message);
	}

}
