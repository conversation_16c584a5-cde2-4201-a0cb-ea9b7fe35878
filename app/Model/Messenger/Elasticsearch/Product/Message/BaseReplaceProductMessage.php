<?php

namespace App\Model\Messenger\Elasticsearch\Product\Message;


use App\Model\Orm\EsIndex\EsIndex;

class BaseReplaceProductMessage
{
	private int $esIndexId;

	public function __construct(
		private int $productId,
		EsIndex $esIndex,
		private array $convertors,
		private array $signals = [],
	)
	{
		$this->esIndexId = $esIndex->id;
	}

	public function getEsIndexId(): int
	{
		return $this->esIndexId;
	}

	public function getId(): int
	{
		return $this->productId;
	}

	public function getConvertors(): array
	{
		return $this->convertors;
	}

	public function getSignals(): array
	{
		return $this->signals;
	}
}
