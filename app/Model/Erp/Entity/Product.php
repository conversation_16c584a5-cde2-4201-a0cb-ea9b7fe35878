<?php declare(strict_types = 1);

namespace App\Model\Erp\Entity;

use App\Model\StringHelper;
use Nette\Utils\ArrayHash;

class Product
{

	/**
	 * extId
	 */
	public int $id;

	public ?string $ean;

	public string $name;

	public ?int $brandId;

	public ?string $brandName;

	/**
	 * nakupni cena bez DPH
	 */
	public float $purchasePrice;

	/**
	 * katalog - dopurucena prodejni MOC
	 */
	public float $recommendedPrice;

	/**
	 * sazba DPH
	 */
	public float $vat;

	public ?string $description = null;

	public ?string $content = null;

	/**
	 * ERP strom cat zanoreni - ID; odd. %|%
	 */
	public ?string $categoryIdsRaw;

	/**
	 * ERP strom cat zanoreni - nazvy; odd. %|%
	 */
	public ?string $categoryPathRaw;

	/**
	 * pocet skladem
	 */
	public int $stock;

	/**
	 * hmotnost v g
	 */
	public ?int $weight;

	/**
	 * hmotnost vcetne jendotky g/kg
	 */
	public ?string $weightUnit = null;

	public function __construct(ArrayHash $values)
	{
		$this->setDataFromWsItem($values);
	}

	public function setDataFromWsItem(ArrayHash $item): void
	{
		$this->id = (int) $item->idZbozi;
		$this->ean = isset($item->ean) ? StringHelper::stringToNull($item->ean) : null;

		$this->name = $item->nazev ?? '';
		$this->description = isset($item->popisKratky) ? StringHelper::stringToNull($item->popisKratky) : null;

		if (isset($item->popisDlouhy)) {
			$item->popisDlouhy = html_entity_decode((string) $item->popisDlouhy);

			$search = ['<![CDATA[', ']]>'];
			$replace = ['', ''];
			$this->content = str_replace($search, $replace, $item->popisDlouhy);
		}

		$this->brandId = isset($item->idBrand) ? StringHelper::intToNull($item->idBrand) : null;
		$this->brandName = isset($item->brand) ? StringHelper::stringTonull($item->brand) : null;

		$this->purchasePrice = isset($item->cenaNakup) ? (float) $item->cenaNakup : 0.0;
		$this->recommendedPrice = isset($item->cenaKatalog) ? (float) $item->cenaKatalog : 0.0;
		$this->vat = isset($item->sazbaDph) ? (float) $item->sazbaDph : 0.0;

		$this->categoryIdsRaw = isset($item->idKtegorieStrom) ? StringHelper::stringToNull($item->idKtegorieStrom) : null;
		$this->categoryPathRaw = isset($item->kategorie) ? StringHelper::stringToNull($item->kategorie) : null;

		$this->stock = isset($item->skladem) ? (int) $item->skladem : 0;
		$this->weight = isset($item->hmotnost) ? StringHelper::intToNull($item->hmotnost) : null;

		if (isset($this->weight)) {
			$this->weightUnit = StringHelper::formatUnits($this->weight);
		}
	}

}
