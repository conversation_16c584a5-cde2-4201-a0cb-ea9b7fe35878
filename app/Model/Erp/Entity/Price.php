<?php declare(strict_types = 1);

namespace App\Model\Erp\Entity;

use DateTimeImmutable;
use Nette\Utils\ArrayHash;

class Price
{

	/**
	 * ERP product ID
	 */
	public int $variantExtId;

	/**
	 * nakupni cena bez DPH
	 */
	public float $purchasePrice;

	/**
	 * katalog - dopurucena prodejni MOC
	 */
	public float $recommendedPrice;

	/**
	 * sazba DPH
	 */
	public float $vat;

	/**
	 * minimalni trvanlivost
	 */
	public DateTimeImmutable|null $minExpiration;

	/**
	 * pocet skladem
	 */
	public int $stock;

	/**
	 * pocet skladem HD
	 */
	public int $stockHD;

	public function __construct(ArrayHash $values)
	{
		$this->setDataFromWsItem($values);
	}

	public function setDataFromWsItem(ArrayHash $item): void
	{
		$this->variantExtId = (int) $item->idZbozi;

		$this->purchasePrice = isset($item->cenaNakup) ? (float) $item->cenaNakup : 0.0;
		$this->recommendedPrice = isset($item->cenaKatalog) ? (float) $item->cenaKatalog : 0.0;
		$this->vat = isset($item->sazbaDph) ? (float) $item->sazbaDph : 0.0;

		$this->minExpiration = isset($item->minExpir) ? new DateTimeImmutable($item->minExpir) : null;

		$this->stock = isset($item->skladem) ? (int) $item->skladem : 0;
		$this->stockHD = isset($item->skladHD) ? (int) $item->skladHD : 0;
	}

}
