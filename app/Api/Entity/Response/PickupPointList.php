<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use Nextras\Dbal\Result\Row;

class PickupPointList extends BasicEntity
{

	/** @var PickupPointListItem[]|PickupPointListMarkerItem[] */
	public readonly array $pickupPoints;

	public function __construct(
		array $pickupPoints,
		bool $onlyMarkers = false
	)
	{
		$items = [];
		foreach ($pickupPoints as $item) {
			assert($item instanceof Row);
			if ($onlyMarkers) {
				$items[] = new PickupPointListMarkerItem($item);
			} else {
				$items[] = new PickupPointListItem($item);
			}
		}

		$this->pickupPoints = $items;
	}

}
