<?php

namespace App\Scheduler;

use App\Scheduler\Message\Test;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Scheduler\RecurringMessage;
use Symfony\Component\Scheduler\Schedule;
use Symfony\Component\Scheduler\ScheduleProviderInterface;
use Symfony\Contracts\Cache\CacheInterface;

// [AsSchedule]
class Schedule<PERSON>rovider implements ScheduleProviderInterface
{

	protected Schedule $schedule;

	public function __construct(
		private readonly LockFactory $lockFactory,
		private readonly CacheInterface $cache,
	)
	{
	}

	public function getSchedule(): Schedule
	{
		return $this->schedule ??= (new Schedule())->add(
			RecurringMessage::every('2 seconds', new Test()),
			RecurringMessage::cron('* * * * *', new Test('cron'))
		)->stateful($this->cache)->lock($this->lockFactory->createLock('schedule'));
	}

}
