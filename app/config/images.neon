parameters:
	noImageFile: '/static/img/illust/noimg.svg'
	mainImageDirectory: '/data/images'
	config:
		imageOriginal:
			resize: TRUE
			width:  3840
			height: 3840

		# quality: [0–100]; defaults to 85 (JPG), 80 (WEBP), 30 (AVIF), 100 (PNG)
		imageSizes:
			# SA - part/head/meta.latte && defail noImageSiez for SVG/GIF
			ogImage:
				width:  1200
				height: 630
				keepRatio: true

			max:
				width: 3840
				height: 3840

			2xl:
				width:  1920
				height: 1920
			2xl-2-1:
				width:  1920
				height: 960
				keepRatio: true
			2xl-16-9:
				width:  1920
				height: 1080
				keepRatio: true

			xl:
				width:  1400
				height: 1400
			xl-2-1:
				width:  1400
				height: 700
				keepRatio: true
			xl-4-3:
				width:  1400
				height: 1050
				keepRatio: true
			xl-16-9:
				width:  1400
				height: 788
				keepRatio: true

			lg:
				width: 750
				height: 750
			lg-2-1:
				width:  750
				height: 375
				keepRatio: true
			lg-4-3:
				width:  750
				height: 563
				keepRatio: true
			lg-9-16:
				width:  750
				height: 1333
				keepRatio: true
			lg-16-9:
				width: 1333
				height:  750
				keepRatio: true

			md:
				width:  560
				height: 560
			md-2-1:
				width:  560
				height: 280
				keepRatio: true
			md-4-3:
				width:  560
				height: 420
				keepRatio: true
			md-16-9:
				width:  560
				height: 315
				keepRatio: true

			sm:
				width:  320
				height: 320
			sm-4-3:
				width:  320
				height: 240
				keepRatio: true
			sm-16-9:
				width:  320
				height: 180
				keepRatio: true

			xs:
				width:  100
				height: 100
			xs-4-3:
				width:  100
				height: 75
				keepRatio: true


			# specific
			# system-message_mobile:
			# 	width: 750
			# 	height: 97
			# 	keepRatio: true
			# system-message_tablet:
			# 	width: 1000
			# 	height: 62
			# 	keepRatio: true
			# system-message_desktop:
			# 	width: 1910
			# 	height: 62
			# 	keepRatio: true

			# bnr_mobile:
			# 	width: 750
			# 	height: 362
			# 	keepRatio: true
			# bnr_tablet:
			# 	width: 1000
			# 	height: 143
			# 	keepRatio: true
			# bnr_desktop:
			# 	width: 1400
			# 	height: 180
			# 	keepRatio: true

			# product_bnr_mobile:
			# 	width: 455
			# 	height: 96
			# 	keepRatio: true
			# product_bnr_desktop: # almost same like a tablet
			# 	width: 753
			# 	height: 125
			# 	keepRatio: true

			# SA - don't delete
			library:
				width:  1540
				height: 1540
			s:
				width:  280
				height: 280
			l:
				width:  800
				height: 800





services:
	- App\Model\Image\Storage\BasicStorage(%config.imageSizes%, %config.WWW_DIR%, mainImageDirectory: %mainImageDirectory%, forcedDomain: %config.imageFromStage%)
	- App\Model\Image\Resizer(%config.WWW_DIR%)
	- App\Model\Image\Setup\ImageSizes(%config.imageSizes%)
	- App\Model\Image\ImageObjectFactory(noImageFile: %noImageFile%, wwwDir: %config.WWW_DIR%)
	- App\Model\Image\BetterImageTypeDetector
	- App\Model\Image\Rotator

