parameters:
	config:
		erp:
			connectors:
				rest:
					endpoint:
					tokenExpire: 55 minutes
					credentials:
						username:
						password:
					verify: true # cURL error 60: SSL certificate problem: unable to get local issuer certificate
					headers:
						sessionID: 0
					verboseLog: true
				ws:
					endpoint: 'http://**************/HomeDelivery/HomeDeliveryWS?WSDL'
					credentials:
						username: "TESThd"
						password: "hdTesT"
					stream_context:
						socket_bindto:
					timeout: # socket timeout (s)
						default: 180
					useSoapClientMock: true
					verboseLog: true

			logToSentry: false

			batches:
				useSleep: false
				sleep: 1 # s

			product:
				import:
					batchLimit: 10
					verboseLog: true
					checkSkipped: false
					removeSkipped: false
				process:
					batchLimit: 100
					verboseLog: true
			price:
				import:
					batchLimit: 10
					verboseLog: true
					checkSkipped: true
					removeSkipped: true
					#deleteImported: false
					#deleteSourceFile: false
			stock:
				import:
					batchLimit: 10
					verboseLog: true
					checkSkipped: true
					removeSkipped: true
					#deleteImported: false
				process:
					batchLimit: 200
					verboseLog: true

			order:
				import:
					batchLimit: 10
					verboseLog: true
					checkSkipped: true
					removeSkipped: true
				process:
					batchLimit: 100
					verboseLog: true

			heureka_review:
				import:
					batchLimit: 10
					verboseLog: true
				process:
					batchLimit: 100
					verboseLog: true

			heureka_shop_review:
				import:
					batchLimit: 10
					verboseLog: true
				process:
					batchLimit: 100
					verboseLog: true
services:

	# connectors
	- App\Model\Erp\Connector\ConnectorRegistry

	- App\Model\Erp\Connector\ProductConnector
	- App\Model\Erp\Connector\StockConnector
	- App\Model\Erp\Connector\PriceConnector
	- App\Model\Erp\Connector\ImageConnector

	- App\Model\Erp\Connector\OrderConnector
	- App\Model\Erp\Connector\HeurekaReviewConnector
	- App\Model\Erp\Connector\HeurekaShopReviewConnector
	- App\Model\Erp\Connector\AlternativeCategoryHeurekaConnector
	- App\Model\Erp\Connector\AlternativeCategoryGoogleConnector
	- App\Model\Erp\Connector\AlternativeCategoryZboziConnector

	- App\Model\Erp\SyncErpOrderService

	# importers
	- App\Model\Erp\Importer\ImporterFactoryRegistry

	- App\Model\Erp\Importer\ProductImporterFactory
	- App\Model\Erp\Importer\StockImporterFactory
	- App\Model\Erp\Importer\PriceImporterFactory

	- App\Model\Erp\Importer\HeurekaReviewImporterFactory
	- App\Model\Erp\Importer\HeurekaShopReviewImporterFactory
	- App\Model\Erp\Importer\AlternativeCategoryHeurekaFactory
	- App\Model\Erp\Importer\AlternativeCategoryGoogleFactory
	- App\Model\Erp\Importer\AlternativeCategoryZboziFactory


	# processors
	- App\Model\Erp\Processor\CommonProcessorFactory

	# readers
	- App\Model\Erp\Processor\Reader\ReaderRegistry

	- App\Model\Erp\Processor\Reader\HeurekaReviewReader
	- App\Model\Erp\Processor\Reader\HeurekaShopReviewReader


	# exporters

monolog:
	channel:
		product_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/product_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		product_process:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/product_process.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		stock_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/stock_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		stock_process:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/stock_process.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		price_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/price_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		price_process:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/price_process.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		order_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/order_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor
		order_process:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/order_process.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		heureka_review_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/heureka_review_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		heureka_review_process:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/heureka_review_process.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		heureka_shop_review_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/heureka_shop_review_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		heureka_shop_review_process:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/heureka_shop_review_process.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

		alternative_category_import:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/alternative_category_import.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor
