{var $anchorName = 'setup'}
{var $icon = $templates . '/part/icons/align-left.svg'}
{var $title = 'Nastavené dárku'}

{var $props = [
title: $title,
id: $anchorName,
icon: $icon,
variant: 'main',
open: true,
classes: ['u-mb-xxs'],
]}

{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{include $templates . '/part/core/checkbox.latte', props: [
			input: $form['conditions']['onFirstOrder'],
			classesLabel: ['title'],
			type: 'checkbox'
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['conditions']['sort'],
			classesLabel: ['title'],
			type: 'number'
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['conditions']['price_amount'],
			classesLabel: ['title'],
			type: 'number'
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['conditions']['price_currency'],
			classesLabel: ['title'],
			type: 'select',
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['conditions']['minPrice'],
			classesLabel: ['title'],
			type: 'number'
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['conditions']['minCount'],
			classesLabel: ['title'],
			type: 'number'
		]}

	{/block}
{/embed}

