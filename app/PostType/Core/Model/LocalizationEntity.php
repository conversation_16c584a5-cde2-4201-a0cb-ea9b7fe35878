<?php declare(strict_types = 1);

namespace App\PostType\Core\Model;

use App\Model\Orm\Mutation\Mutation;
use Nette\Utils\ArrayHash;

interface LocalizationEntity
{

	public function getId(): int;
	public function getMutation(): Mutation;

	public function setMutation(Mutation $mutation): void;
	public function getParent(): ?ParentEntity;

	public function setParent(ParentEntity $parentEntity): void;

	public function getCfSchemeJson(): string;
	public function getCfContent(): string;
	public function setCf(mixed $customFields): void;

	public function getCcSchemeJson(): string;
	public function getCcJson(): string;
	public function getCcModulesJson(): string;
	public function setCc(ArrayHash $customContent): void;

	public function getName(): string;
	public function setName(string $name): void;

}
