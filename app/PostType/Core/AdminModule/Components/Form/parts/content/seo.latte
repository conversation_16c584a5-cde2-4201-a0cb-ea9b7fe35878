{var $anchorName = 'seo'}
{var $icon = $templates.'/part/icons/google.svg'}
{var $title = 'SEO'}
{default $appendInputs = []}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}

{if !isset($nameForAlias)}
	{var $nameForAlias = $form['localization']['name']->getValue()}
{/if}



{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		{include $templates.'/part/core/inp.latte' props: [
			input: $form['routable']['alias'],
			classesLabel: ['title'],
			btn: 'Z názvu',
			data: [
				controller: 'Alias',
				alias-url-value: '/superadmin/page/regenerate-alias-mutation',
				alias-lang-value: $mutation->langCode,
				alias-id-value: $parent->id,
				alias-name-value: $nameForAlias,
				action: 'ProductTitle:generateAlias@window->Alias#generate',
			],
			dataInp: [
				alias-target: 'inp'
			],
			dataBtn: [
				action: 'Alias#generate',
			]
		]}

		{include $templates.'/part/core/inp.latte' props: [
			input: $form['routable']['nameAnchor'],
			classesLabel: ['title'],
			dataInp: [
				controller: 'ProductTitleSeo',
				action: 'ProductTitle:changeTitle@window->ProductTitleSeo#updateValue',
				producttitleseo-lang-value: $mutation->langCode
			]
		]}

		{include $templates.'/part/core/inp.latte' props: [
			input: $form['routable']['nameTitle'],
			classesLabel: ['title'],
			dataInp: [
				controller: 'ProductTitleSeo',
				action: 'ProductTitle:changeTitle@window->ProductTitleSeo#updateValue',
				producttitleseo-lang-value: $mutation->langCode
			]
		]}

		{foreach $appendInputs as $inputName}
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['routable'][$inputName],
				type: 'text',
				classesLabel: ['title']
			]}
		{/foreach}
		{include $templates.'/part/core/inp.latte' props: [
			input: $form['routable']['description'],
			type: 'textarea',
			classesLabel: ['title']
		]}

		{if isset($form['routable']['keywords'])}
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['routable']['keywords'],
				type: 'text',
				classesLabel: ['title']
			]}
		{/if}


		{include $templates.'/part/core/inp.latte' props: [
			input: $form['routable']['aliasHistory'],
			type: 'textarea',
			classesLabel: ['title']
		]}

	{/block}
{/embed}




