<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\DataGrid;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nette\Application\UI\Control;
use Nette\Utils\Html;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Tracy\Debugger;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	use HasMutationColumn;

	private readonly DataGridDefinition $dataGridDefinition;

	public function __construct( // @phpstan-ignore-line
		private readonly string $baseEntityName,
		private readonly ICollection $collection,
		private readonly Translator $translator,
		private readonly Orm $orm,
		?DataGridDefinition $dataGridDefinition = null,
	)
	{
		$this->dataGridDefinition = $dataGridDefinition ?? new DataGridDefinition();
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		if ($this->dataGridDefinition->dataGrid !== null) {
			$grid = $this->dataGridDefinition->dataGrid;
		} else {
			$grid = new \Ublaboo\DataGrid\DataGrid();
		}
		$grid->setDataSource($this->collection);

		$firstItem = $this->collection->limitBy(1)->fetch();

		if ($firstItem !== null && $firstItem->getMetadata()->hasProperty($this->baseEntityName)) {
			$grid->addColumnText('internalName', 'internalName', $this->baseEntityName . '.internalName')->setSortable()->setFilterText();
		}

		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();


		foreach ($this->dataGridDefinition->extenders as $extender) {
			($extender->getImproveFunction())($grid);
		}


		if ($firstItem !== null && $firstItem->getMetadata()->hasProperty('mutation')) {
			$this->addColumnMutation($grid);
		}

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setRowCallback(function(Entity $item, Html $tr) {
			if ($item instanceof BaseEntity) {
				if (!$item->isPublished()) {
					$tr->addClass("row-disabled");
				}
			}
		});

		$grid->setTranslator($this->translator);

		foreach ($this->dataGridDefinition->beforeRenderExtenders as $extender) {
			($extender->getImproveFunction())($grid);
		}

		$grid->setTemplateFile(__DIR__ . '/dataGridTemplate.latte');

		return $grid;
	}

}
