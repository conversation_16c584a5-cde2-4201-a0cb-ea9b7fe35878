<?php declare(strict_types = 1);

namespace App\PostType\CalendarTag\FrontModule\Presenters;

use App\FrontModule\Presenters\BasePresenter;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTag;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;

/**
 * @method CalendarTag getObject()
 */
final class CalendarTagPresenter extends BasePresenter
{

	private CalendarTagLocalization $calendarTagLocalization;

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDetail(CalendarTagLocalization $object): void
	{
		$this->setObject($object);
		$this->calendarTagLocalization = $object;
	}


	public function renderDetail(): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $this->calendarTagLocalization;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = 1;
		$allPublicCalendars = $this->calendarTagLocalization->calendarsPublic;
		$paginator->itemCount = $allPublicCalendars->count();

		$this->template->calendarTag = $this->calendarTagLocalization;
		$this->template->calendars = $allPublicCalendars->limitBy($paginator->itemsPerPage, $paginator->offset);

		if ($this->isAjax()) {
			$this->redrawControl('articles');
		}
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
