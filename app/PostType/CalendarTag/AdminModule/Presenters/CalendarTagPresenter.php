<?php declare(strict_types = 1);

namespace App\PostType\CalendarTag\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\CalendarTag\Model\CalendarTagLocalizationFacade;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class CalendarTagPresenter extends BasePresenter
{

	private const string ORM_REPOSITORY_NAME = 'calendarTag';

	private CalendarTagLocalization $calendarTagLocalization;

	public function __construct(
		private DataGridFactory $dataGridFactory,
		private FormFactory $calendarTagFormFactory,
		private ShellFormFactory $shellFormFactory,
		private CalendarTagLocalizationFacade $calendarTagLocalizationFacade,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$calendarTagLocalization = $this->orm->calendarTagLocalization->getById($id);
		if ($calendarTagLocalization === null) {
			$this->redirect('default');
		}

		$this->calendarTagLocalization = $calendarTagLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->calendarTagLocalization->findAll());
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->calendarTagFormFactory->create($this->calendarTagLocalizationFacade, $this->calendarTagLocalization, $userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity:null, facade: $this->calendarTagLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
