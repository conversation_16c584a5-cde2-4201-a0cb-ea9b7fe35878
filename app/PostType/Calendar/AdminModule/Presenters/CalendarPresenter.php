<?php declare(strict_types = 1);

namespace App\PostType\Calendar\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Calendar\AdminModule\Components\DataGrid\CalendarDataGridPrescription;
use App\PostType\Calendar\AdminModule\Components\Form\CalendarFormPrescription;
use App\PostType\Calendar\Model\CalendarLocalizationFacade;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class CalendarPresenter extends BasePresenter
{

	public const ORM_REPOSITORY_NAME = 'calendar';

	private CalendarLocalization $calendarLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly CalendarLocalizationFacade $calendarLocalizationFacade,
		private readonly CalendarFormPrescription $calendarFormPrescription,
		private readonly CalendarDataGridPrescription $calendarDataGridPrescription,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$calendar = $this->orm->calendarLocalization->getById($id);
		if ($calendar === null) {
			$this->redirect('default');
		}

		$this->calendarLocalization = $calendar;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(
			baseEntityName: self::ORM_REPOSITORY_NAME,
			collection: $this->orm->calendarLocalization->findAll(),
			dataGridDefinition: $this->calendarDataGridPrescription->get(),
		);
	}



	public function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->formFactory->create($this->calendarLocalizationFacade, $this->calendarLocalization, $userEntity, $this->calendarFormPrescription->get($this->calendarLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, facade: $this->calendarLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

}
