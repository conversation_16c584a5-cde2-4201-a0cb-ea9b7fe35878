<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model\Orm\Tag;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasStaticCache;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Tag\Model\TagType;
use App\Utils\DateTime;
use Nextras\Dbal\QueryBuilder\QueryBuilder;
use Nextras\Dbal\Result\Result;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Tag>
 */
final class TagMapper extends DbalMapper
{

	use HasCamelCase;
	use HasStaticCache;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'tag';
}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		$conventions->manyHasManyStorageNamePattern = '%s_x_%s';    // @phpstan-ignore-line
		return $conventions;
	}

	private function getProductPublicBuilder(): QueryBuilder
	{
		return $this->builder()
			->select('product.*')
			->from('product', 'product')
			->joinInner('[product_localization] as pl', '[product.id] = [pl.productId]');
	}

	/** @return ICollection<Tag> */
	public function findProductsWithPresent(?int $limit = null, ?int $offset = null): ICollection
	{
		$builder = $this->getProductPublicBuilder()
			->joinInner('[product_product] as pp', '[product.id] = [pp.mainProductId]')
			->where('pp.type = %s ', ProductProduct::TYPE_PRESENT)
			->limitBy($limit, $offset);

		return $this->toCollection($builder);
	}

	public function findProductsInProductTreeOrderedByScore(Tree $tree, int $limit = 0, int $offset = 0): array
	{
		$builder = $this->getProductPublicBuilder()
			->joinInner('[product_tree] as pt', '[product.id] = [pt.productId]')
			->joinLeft('[product_parameter] as pp', '[product.id] = [pp.productId]')
			->where('pt.treeId = %i', $tree->id)
			->orderBy('product.score ' . ICollection::DESC)
			->limitBy($limit ?: null, $offset ?: null);

		return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');
	}


	public function findProductsByRequalificationPossibility(int $limit = 0, int $offset = 0): array
	{
		$builder = $this->getProductPublicBuilder()
			->where('pl.requalificationPossibility > 0')
			->limitBy($limit ?: null, $offset ?: null);

		return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');
	}

	public function findProductsToPreorderWithPublishDate(int $limit = 0, int $offset = 0): array
	{
		//$twoDaysAgo = DateTime::from('now')->modify('-2 days');

		$builder = $this->getProductPublicBuilder()
			->joinLeft('[product_parameter] as pp', '[product.id] = [pp.productId]')
			->joinLeft('[parameter] as parameter', '[parameter.id] = [pp.parameterId]')
			->joinLeft('[parameter_value] as pv', '[pv.id] = [pp.parameterValueId]')
			->andWhere('product.isInPrepare = true')
			->andWhere('parameter.uid = %s', Parameter::UID_PUBLISH_DATE)
			->andWhere('pv.internalValue IS NOT NULL')
			//->andWhere('STR_TO_DATE([pv.internalValue], "%%d.%%m.%%Y") > %dt', $twoDaysAgo)
			->andWhere('(DATEDIFF(STR_TO_DATE([pv.internalValue], "%%d.%%m.%%Y"), NOW()) >= -2)')
			->orderBy('STR_TO_DATE([pv.internalValue], "%%d.%%m.%%Y") DESC')
			->limitBy($limit ?: null, $offset ?: null);

		return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');
	}

	public function removeAssignee(TagType $tagType, Product $product): void
	{
		$this->connection->query('
		DELETE tag_x_product.*
		FROM tag_x_product
    	LEFT JOIN tag on tag.id = tag_x_product.tagId
		WHERE
			tag.type = "%s" AND
			tag_x_product.productId = %i', $tagType->value, $product->getPersistedId());
	}

	public function removeAssignees(TagType $tagType, array $products): void
	{
		if (empty($products)) {
			return;
		}

		$this->connection->query('
		DELETE tag_x_product.*
		FROM tag_x_product
    	LEFT JOIN tag on tag.id = tag_x_product.tagId
		WHERE
			tag.type = "' . $tagType->value . '" AND
			tag_x_product.productId IN %i[]', $products);
	}

	public function getAllProcessedProducts(TagType $tagType, \Nette\Utils\DateTime $date, ?int $offset = null, ?int $limit = null): array
	{
		//return $this->loadCache(
		//	$this->createCacheKey($tagType->name),
		//	function () use ($tagType, $date) {
		//		return
				$b = $this->builder()->select('txp.productId')
					->from('tag_x_product', 'txp')
					->joinLeft('tag as t', '[t.id] = [txp.tagId]')
					->where('t.type = %s', $tagType->name)
					->andWhere('txp.edited >= %dt', $date);
				if ($limit !== null && $offset !== null) {
					$b->limitBy($limit, $offset);
				}

				return $this->connection->queryByQueryBuilder(
					$b
				)->fetchPairs(null, 'productId');
		//	}
		//);
	}

	public function getProductByDate(TagType $tagType, \Nette\Utils\DateTime $date, int $offset = 0, int $batchSize = 0): array
	{
		$builder = $this->builder()
			->from('tag_x_product', 'txp')
			->joinLeft('[tag] as t', '[txp.tagId] = [t.id]')
			->select('txp.productId')
			->where('t.type = %s', $tagType->name)
			->andWhere('txp.edited < %dt', $date)
			->orderBy('txp.productId')
			->limitBy($batchSize, $offset);

		return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'productId');
	}

	public function updateTagTimestamp(TagType $tagType, Product $product): void
	{
		$tagTypeId = $this->loadCache($this->createCacheKey('tagTypeId', $tagType), function () use ($tagType) {
			$tag = $this->connection->query('SELECT `id` FROM `tag` WHERE `type` = %s', $tagType->name)->fetch();
			return $tag->id;
		});

		$this->connection->query('
			UPDATE tag_x_product
			SET tag_x_product.edited = %dt
			WHERE tag_x_product.productId = %i
			AND tag_x_product.tagId = %i
			', DateTime::from('now'), $product->id, $tagTypeId);
	}

	public function getProductRelations(Product $product): Result
	{
		return $this->connection->query(
			'SELECT txp.*, t.internalName, t.id as tagId FROM tag_x_product as txp
         			LEFT JOIN tag as t on (t.id = txp.tagId)
         			LEFT JOIN tag_localization as tl on (t.id = tl.tagId)
         			where productId = %i order by tl.position',
			$product->id
		);
	}

	public function replaceRelationRow(Product $product, Tag $tag, ?\DateTimeImmutable $from, ?\DateTimeImmutable $to): void
	{
		$data = [
			'tagId' => $tag->id,
			'productId' => $product->id,
			'from' => $from,
			'to' => $to,
			'edited' => new DateTimeImmutable(),
		];

		$this->connection->query('INSERT INTO [tag_x_product] %values ON DUPLICATE KEY UPDATE %set', $data, $data);
	}

	/**
	 * @return ICollection<Tag>
	 */
	public function findActiveTags(Product $product, ?Mutation $mutation = null, int $limit = 3): ICollection
	{
		$builder = $this->builder()
			->from('tag', 't')
			->joinInner('[tag_localization] as tl', '[t.id] = [tl.tagId]')
			->joinLeft('[tag_x_product] as txp', '[txp.tagId] = [t.id]')
			->joinLeft('[tag_localization_x_product] as tlxp', '[tlxp.tagLocalizationId] = [tl.id]')
			->select('t.*')
			->where(
				'
				(
					txp.productId = %i and (
						t.type != %s or (
							t.type = %s and (txp.to is null or txp.to > NOW())
							and (txp.from is null or txp.from < NOW())
						)
					)

					or

					tlxp.productId = %i and (
						t.type != %s or (
							t.type = %s and (tlxp.to is null or tlxp.to > NOW())
							and (tlxp.from is null or tlxp.from < NOW())
						)
					)
				)
				',
				$product->id,
				TagType::custom,
				TagType::custom,
				$product->id,
				TagType::custom,
				TagType::custom,
			)
			->groupBy('t.id')
			->orderBy('tl.position')
			->limitBy($limit);

		if ($mutation !== null) {
			$builder->andWhere('tl.mutationId = %i AND tlxp.id IS NOT NULL', $mutation->id);
		}

		return $this->toCollection($builder);
	}

}
