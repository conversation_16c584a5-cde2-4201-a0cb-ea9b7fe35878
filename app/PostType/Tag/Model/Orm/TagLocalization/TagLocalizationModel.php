<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model\Orm\TagLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasStaticCache;
use Nextras\Orm\Collection\ICollection;

final class TagLocalizationModel
{

	use HasStaticCache;

	public function __construct(
		private readonly TagLocalizationRepository $tagLocalizationRepository,
	)
	{
	}

	/**
	 * @return ICollection<TagLocalization>
	 */
	public function findByProduct(Product $product, Mutation $mutation): ICollection
	{
		return $this->loadCache($this->createCacheKey('getByForProduct', $product->id), function () use ($product, $mutation) {
			$tagIds = $product->findActiveTags(mutationDependent: true)->fetchPairs(null, 'id');
			return $this->tagLocalizationRepository->findBy([
				'tag->id' => $tagIds,
				'mutation' => $mutation,
			]);
		});
	}

}
