{varType App\Model\Product $product}
{varType SuperKoderi\FakePaginator $paginator}

{snippet productReviews}
	<div n:if="$totalReviews > 0" class="u-mb-sm">
		{* Řazení *}
		{snippetArea reviewsSort}
			{include $templates.'/part/form/part/sort.latte', class=>'u-mb-sm u-mb-lg@md', showing=>false, sortOptions=>['latest', 'best', 'worst']}
		{/snippetArea}

		{varType App\Model\ProductReview[] $reviews}
		<div class="c-reviews">
			<div n:snippet="reviewsList" data-ajax-append>
				{foreach $reviews as $review}
					<div n:class="u-mb-sm">
						{control 'multiReview-' . $review->id}
					</div>
				{/foreach}
			</div>

			{snippet reviewsListPagerMore}
				<p n:if="!$paginator->isLast()" class="c-reviews__more u-mb-0">
					<a n:href="more! pager-more => 1, pager-page => $paginator->getPage() +1" class="btn btn--shadow btn--arrow btn--sm" data-naja data-naja-loader="body" data-naja-history="off">
						<span class="btn__text">
							{_"review_btn_more"}
							{('angle-down')|icon, 'btn__icon'}
						</span>
					</a>
				</p>
			{/snippet}
		</div>
	</div>
{/snippet}

