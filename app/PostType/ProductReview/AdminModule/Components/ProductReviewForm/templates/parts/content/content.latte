{var $anchorName = 'content'}
{var $icon = $templates . '/part/icons/align-left.svg'}
{var $title = 'Obsah'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}


{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['name'],
			classLabel: ['title'],
			label: $form['name']->name
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['email'],
			classLabel: ['title'],
			label: $form['email']->name
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['status'],
			classLabel: ['title'],
			label: $form['status']->name,
			type: 'select'
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['stars'],
			classLabel: ['title'],
			label: $form['stars']->name,
			type: 'number'
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['source'],
			classLabel: ['title'],
			label: $form['source']->name,
			type: 'select'
		]}

		{include $templates.'/part/core/inp.latte' props: [
			label: 'text',
			input: $form['text'],
			classesLabel: ['title'],
			type: 'textarea',
			rows: 10,
		]}

{*		{if $productReview->getCfScheme()}*}
{*				{include './parts/content/custom-fields.latte', form=>$form}*}
{*		{/if}*}
		{include $corePartsDirectory . '/content/custom-fields.latte',
			form => $form,
			cfObject => $entity,
			title => 'Klady & Zápory',
			containerName => 'setup',
			itemName => 'cf'
		}
	{/block}
{/embed}
