<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\SystemMessage\AdminModule\Components\SystemMessagesList\SystemMessagesPrescription;
use App\PostType\SystemMessage\Model\SystemMessageFacade;
use App\PostType\SystemMessage\Model\Orm\SystemMessage;
use App\PostType\SystemMessage\AdminModule\Components\SystemMessageForm\SystemMessageFactory;
use App\PostType\SystemMessage\AdminModule\Components\SystemMessageForm\SystemMessageForm;

final class SystemMessagePresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'SystemMessage';

	private SystemMessage $systemMessage;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly SystemMessageFactory $systemMessageFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly SystemMessageFacade $systemMessageFacade,
		private readonly SystemMessagesPrescription $systemMessagesPrescription,
	)
	{
		parent::__construct();
	}


	public function actionEdit(int $id): void
	{
		/**
		 * @var SystemMessage|null $systemMessage;
		 */
		$systemMessage = $this->orm->systemMessage->getById($id);

		if ($systemMessage === null) {
			$this->redirect('default');
		}

		$this->systemMessage = $systemMessage;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->systemMessage->findAll(), $this->systemMessagesPrescription->get());
	}


	protected function createComponentSystemMessageForm(): SystemMessageForm
	{
		return $this->systemMessageFactory->create($this->systemMessage, $this->userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, facade: $this->systemMessageFacade);
	}


	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
