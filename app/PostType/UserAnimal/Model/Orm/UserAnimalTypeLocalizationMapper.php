<?php declare(strict_types = 1);

namespace App\PostType\UserAnimal\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<UserAnimalTypeLocalization>
 */
class UserAnimalTypeLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'user_animal_type_localization';
	}

	/**
	 * @return ICollection<UserAnimalTypeLocalization>
	 */
	public function getUserAnimalTypes(Mutation $mutation): ICollection
	{
		$builder = $this->builder();
		$builder->select('atl.*')
			->from('user_animal_type_localization as atl')
			->joinInner('[mutation] as m', '[m.id] = [atl.mutationId]')
			->andWhere('m.id = %i', $mutation->id)
			->andWhere('atl.public = 1')
			->orderBy('atl.name');

		return $this->toCollection($builder);
	}

}
