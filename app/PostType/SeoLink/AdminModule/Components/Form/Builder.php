<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\AdminModule\Components\Form;

use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;
use App\PostType\SeoLink\Model\Orm\SeoLink;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;

final class Builder
{

	public function __construct(
		private readonly CoreBuilder $coreBuilder,
	)
	{
	}

	public function build(\Nette\Application\UI\Form $form, SeoLinkLocalization $seoLinkLocalization, array $postData): void
	{
		$this->coreBuilder->addLocalization($form, $seoLinkLocalization);
		$this->coreBuilder->addRoutable($form, $seoLinkLocalization);
		$this->coreBuilder->addParent($form, $seoLinkLocalization->getParent());

		$this->addParameterValuesToForm($form, $seoLinkLocalization->seoLink, $postData);

		$this->coreBuilder->addButtons($form);
	}

	private function addParameterValuesToForm(\Nette\Application\UI\Form $form, SeoLink $seoLink, array $postData): void
	{
		$parameterValuesContainer = $form->addContainer('parameterValues');
		if (isset($postData['parameterValues'])) {
			foreach ($postData['parameterValues'] as $parameterValueKey => $parameterValue) {
				$parameterValueContainer = $parameterValuesContainer->addContainer($parameterValueKey);
				$parameterValueContainer->addHidden('id');
				$parameterValueContainer->addText('name', 'name');
			}
		} else {
			foreach ($seoLink->parameterValues as $parameterValueKey => $parameterValue) {
				$parameterValueContainer = $parameterValuesContainer->addContainer($parameterValueKey);
				$parameterValueContainer->addHidden('id', $parameterValue->id);
				$parameterValueContainer->addText('name', 'name')->setDefaultValue($parameterValue->internalValue);
			}
		}

		// fake
		$parameterValueContainer = $parameterValuesContainer->addContainer('newItemMarker');
		$parameterValueContainer->addHidden('id');
		$parameterValueContainer->addText('name', 'name');
	}

}
