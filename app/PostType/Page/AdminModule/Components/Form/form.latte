{varType App\PostType\Page\Model\Orm\Tree $entityLocalization}
{varType App\PostType\Page\Model\Orm\Tree $parent}

<form n:name="form" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $corePartsDirectory . '/header.latte', entityLocalization => $entityLocalization, hrefClose=>false}
	</div>
	{input id}
	{input lastEdited}

	<div class="main__content scroll">


		<ul class="message message-error" n:if="$form->hasErrors()">
			<li n:foreach="$form->errors as $error">{$error}</li>
		</ul>

		{include './parts/content/content.latte', form => $form}

		{include $corePartsDirectory . '/content/validity.latte', form => $form}
		{include $corePartsDirectory . '/content/seo.latte', form => $form, nameForAlias=>$tree->name, appendInputs: ['nameAnchorBreadcrumb']}
		{include './parts/content/settings.latte', form => $form, entityLocalization=>$tree}
		{include $corePartsDirectory . '/content/coreCustomItems.latte', form => $form, entityLocalization=>$tree}
	</div>



	<div class="main__content-side scroll">
		{include $corePartsDirectory . '/side/btns.latte', showDeleteButton => ($tree->parent !== null)}
		{include './parts/side/path.latte' entityLocalization=>$tree}
		{control language}
		{include './parts/side/state.latte' , form => $form}
		{include './parts/side/template.latte', form => $form}
		{include $corePartsDirectory . '/side/edits.latte'}
	</div>

	{capture $newItemTemplate}
		{include './parts/newItemTemplate.latte', form=>$form}
	{/capture}

</form>

{$newItemTemplate}
{*{include $templates . '/part/core/shellFormOverlay.latte'}*}


