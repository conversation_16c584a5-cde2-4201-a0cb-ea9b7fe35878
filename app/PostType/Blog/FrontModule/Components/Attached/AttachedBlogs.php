<?php declare(strict_types = 1);

namespace App\PostType\Blog\FrontModule\Components\Attached;

use App\Model\TranslatorDB;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use Mpdf\Tag\Em;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class AttachedBlogs extends Control
{

	public function __construct(
		private Blog $object,
		private TranslatorDB $translator
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->object = $this->object;
		$this->template->blogs = $this->getBlogs();
		$this->template->templates = FE_TEMPLATE_DIR;

		$this->template->render(__DIR__ . '/attachedBlogs.latte');
	}


	/**
	 * @return EmptyCollection<BlogLocalization>
	 */
	protected function getBlogs(): EmptyCollection
	{
//		$withoutBlogIds = [$this->object->id];
//
//		$blogs = $this->object->attachedBlogs->toCollection()->findBy([])->limitBy($this->limit)->fetchPairs('id');
//		$withoutBlogIds = array_merge($withoutBlogIds, array_keys($blogs));
//
//
//		if (count($blogs) < $this->limit) { // ostatni clanky tagu
//			foreach ($this->object->blogTagsPublic as $tag) {
//				$blogs += $tag->blogsPublic->findBy(['id!=' => $withoutBlogIds])->fetchPairs('id');
//			}
//		}
//
//		if (count($blogs) < $this->limit) { // ostatni clanky kategorii
//			foreach ($this->object->categoriesPublic as $category) {
//				$blogs += $category->blogsPublic->findBy(['id!=' => $withoutBlogIds])->fetchPairs('id');
//			}
//		}
//
//		$blogs = array_slice($blogs, 0, $this->limit, true);
//		return new ArrayIterator($blogs);
		/** @var EmptyCollection<BlogLocalization> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $emptyCollection;
	}

}
