{default $class = false}
{default $categories = []}

<div n:if="count($categories)" n:class="c-hero-categories, $class">
	<ul class="c-hero-categories__list">
		<li n:foreach="$categories as $category" n:ifcontent class="c-hero-categories__item">
			{php $type = $category->link->toggle}
			{php $page = isset($category->link->systemHref) && isset($category->link->systemHref->page) ? $category->link->systemHref->page->getEntity() ?? false : false}
			{php $href = $category->link->customHref??->href ?? false}
			{php $hrefNameSystem = $category->link->systemHref??->hrefName ?? false}
			{php $hrefNameCustom = $category->link->customHref??->hrefName ?? false}
			{php $customImg = isset($category->image) ? $category->image->getEntity() ?? false : false}
			{php $crossroadImg = isset($page->cf->base->crossroadImage) ? $page->cf->base->crossroadImage->getEntity() ?? false : false}
			{php $img = $customImg ? $customImg : $crossroadImg}

			{define #linkContent}
				{if $img}
					<img class="c-hero-categories__img img img--4-3 img--contain" src="{$img->getSize('sm')->src}" alt="" loading="lazy">
				{else}
					<img class="c-hero-categories__img img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
				{/if}
				<span class="c-hero-categories__name">
					{$name}
				</span>
				<span n:if="$category->tag ?? false" class="c-hero-categories__flag flag flag--green">{$category->tag}</span>
				<span class="c-hero-categories__arrow">{('arrow-right-thin')|icon}</span>
			{/define}

			{if $type == 'systemHref' && $page}
				<a href="{plink $page}" n:ifcontent class="c-hero-categories__link">
					{include #linkContent, name: $hrefNameSystem ? $hrefNameSystem : $page->nameAnchor}
				</a>
			{elseif $type == 'customHref' && $href && $hrefNameCustom}
				<a href="{$href}" class="c-hero-categories__link" target="_blank" rel="noopener noreferrer">
					{include #linkContent, name: $hrefNameCustom}
				</a>
			{/if}
		</li>
	</ul>
</div>