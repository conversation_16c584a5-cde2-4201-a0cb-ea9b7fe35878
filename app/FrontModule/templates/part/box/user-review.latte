{default $class = false}
{default $type = false}
{default $review = false}
{default $product = false}

<div n:if="$review || $product" n:class="b-user-review, $class">
		<a href="{plink $product}">
			{if $product->firstImage}
				<img src="{$product->firstImage->getSize('sm')->src}" alt="{$product->nameAnchor}" loading="lazy">
			{else}
				<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
			{/if}
		</a>
	{include $templates.'/part/core/type.latte', class: 'b-user-review__type', product: $product}

	<p class="b-user-review__rating">
		{if $type == 'rated'}
			{if $review}
				{include $templates.'/part/core/stars.latte', class=>'b-user-review__stars', stars=>$review->stars, rating=>$review->stars * 100 / 5}
				<a href="{plink $pages->productReviewAdd, productId => $product->id, userView: 'src'}" class="b-user-review__link" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--productReviewAdd">{_"btn_edit"}</a>
			{/if}
		{else}
			<a href="{plink $pages->productReviewAdd, productId => $product->id, userView: 'src'}" class="b-user-review__link item-icon" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--productReviewAdd">
				{('star')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{_"btn_rate_product"}
				</span>
			</a>
		{/if}
	</p>
</div>
