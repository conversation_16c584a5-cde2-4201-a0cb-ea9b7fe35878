{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $items = []}

<div n:class="b-coverage, $class, u-maw-8-12, u-mx-auto">
	<div class="grid grid--middle">
		<div class="grid__cell size--6-12@lg">
			<h2 n:if="$title" class="b-coverage__title u-mb-0">{$title}</h2>
		</div>
		<div class="grid__cell size--6-12@lg">
			<p n:if="count($items)" class="b-coverage__items">
				{foreach $items as $item}
					{if $iterator->isOdd()} {* Otevření nového sloupce pro každé dva prvky *}
						<span class="b-coverage__col">
					{/if}
							<span n:if="$item->text ?? false" class="b-coverage__item">
								{('check-circle')|icon}
								{$item->text}
							</span>
					{if $iterator->isEven() || $iterator->isLast()} {* Uzavření sloupce po dvou prvcích nebo na posledním prvku *}
						</span>
					{/if}
				{/foreach}
			</p>
		</div>
	</div>
</div>