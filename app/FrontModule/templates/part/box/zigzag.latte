{default $class = 'u-mb-md u-mb-2xl@md'}
{default $items = []}
{default $reversed = false}
{default $numbered = false}
{default $bg = false}

<div n:if="count($items)" n:class="b-zigzag, $class, $reversed ? b-zigzag--reversed">
	{php $hasLinks = false}
	{foreach $items as $item}
		{php $links = $item->links ?? []}
		{if count($links)}
			{php $hasLinks = true}
			{breakIf $hasLinks}
		{/if}
	{/foreach}

	<div n:if="$bg" class="b-popular__decorations"></div>
	<div n:tag-if="$bg" class="b-zigzag__bg b-popular__content-inner">
		<ul n:tag="$numbered ? 'ol'" n:class="b-zigzag__list">
			<li n:foreach="$items as $item" class="b-zigzag__item">
				<div n:class="u-mx-auto, $hasLinks ? u-maw-10-12 : u-maw-8-12">
					<div class="b-zigzag__grid grid grid--middle grid--x-sm">
						<div class="b-zigzag__cell b-zigzag__cell--img grid__cell">
							{php $imgs = $item->images??->fetchAll() ?? []}
							<p n:class="b-zigzag__imgs, 'b-zigzag__imgs--' . count($imgs), u-mb-0">
								{if count($imgs) == 1}
									{include #img, i: 0}
								{elseif count($imgs) == 2}
									<span class="b-zigzag__img-col">
										{include #img, i: 0}
									</span>
									<span class="b-zigzag__img-col">
										{include #img, i: 1}
									</span>
								{else}
									<span class="b-zigzag__img-col">
										{include #img, i: 0}
										{include #img, i: 1}
									</span>
									<span class="b-zigzag__img-col">
										{include #img, i: 2}
										{include #img, i: 3}
									</span>
								{/if}
							</p>
						</div>
						<div class="b-zigzag__cell b-zigzag__cell--content grid__cell u-mb-last-0">
							<p n:if="$item->flag ?? false" class="b-zigzag__flag flag flag--blue-light u-fw-n u-mb-sm">{$item->flag}</p>
							<p n:if="$item->approved ?? false" class="approved u-mb-xs">
								<img class="img img--circle" src="/static/img/illust/popular.svg" alt="" loading="lazy" width="70" height="70">
								<img class="img img--circle" src="/static/img/illust/alex.webp" alt="Alex" loading="lazy" width="70" height="70">
							</p>
							{include $templates.'/part/box/content.latte', class: 'b-zigzag__content', content: $item->content ?? false}
							{php $links = $item->links ?? []}
							<ul n:if="count($links)" class="b-zigzag__sublist">
								<li n:foreach="$links as $link">
									{php $type = $link->link->toggle}
									{php $page = isset($link->link->systemHref) && isset($link->link->systemHref->page) ? $link->link->systemHref->page->getEntity() ?? false : false}
									{php $href = $link->link->customHref??->href ?? false}
									{php $hrefNameSystem = $link->link->systemHref??->hrefName ?? false}
									{php $hrefNameCustom = $link->link->customHref??->hrefName ?? false}

									{if $type == 'systemHref' && $page}
										<a href="{plink $page}" n:ifcontent class="b-zigzag__link">
											{if $hrefNameSystem}
												{$hrefNameSystem}
											{else}
												{$page->nameAnchor}
											{/if}
											{('angle-right')|icon}
										</a>
									{elseif $type == 'customHref' && $href && $hrefNameCustom}
										<a href="{$href}" class="b-zigzag__link" target="_blank" rel="noopener noreferrer">
											{$hrefNameCustom}
											{('angle-right')|icon}
										</a>
									{/if}
								</li>
							</ul>
						</div>
					</div>
				</div>
			</li>
		</ul>
	</div>
</div>

{define #img}
	{default $i = 0}
	<img n:if="isset($imgs[$i])" class="b-zigzag__img img" src="{$imgs[$i]->getSize('md')->src}" alt="{$imgs[$i]->getAlt($mutation)}" loading="lazy">
{/define}