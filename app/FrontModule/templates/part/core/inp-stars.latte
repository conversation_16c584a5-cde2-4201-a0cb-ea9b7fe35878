{default $class = false}
{default $name = false}
{default $form}

<p n:if="$form && $name" n:class="inp-stars, $class, $form[$name]->errors ? has-error">
	<input disabled checked class="inp-stars__inp inp-stars__inp--empty" name="{$name}" value="0" type="radio">

	{foreach $form[$name]->items as $k=>$inp}
		<input class="inp-stars__inp" type="radio" id="star-{$k|number:1,'',''}" name="{$name}" value="{$k}"{if $form[$name]->value == $k || $form[$name]->value == null} checked="checked"{/if} required>
		<label class="inp-stars__item" for="star-{$k|number:1,'',''}">
			<span class="inp-stars__inner">
				{include $FE_TEMPLATE_DIR . '/part/core/stars.latte', class=>'inp-stars__stars', percentage=>$k * 20, type=>'full'}
				{_$inp}
			</span>
			{('star-full')|icon, 'inp-stars__icon'}
		</label>
	{/foreach}
</p>
