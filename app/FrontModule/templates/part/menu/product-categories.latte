{default $class = false}
{if $product->attachCategories->count()}
	<nav n:class="m-product-categories, $class, u-mb-last-0">
		<h3 class="m-product-categories__title u-mb-xxs u-mb-xs@md">
			{_title_product_categories}
		</h3>

		{foreach $product->attachCategories as $categoryMain}
			<p class="m-product-categories__item">
				{var $pathItems = []}
				{foreach $categoryMain->pathItems as $item}
					{continueIf $item->uid === App\PostType\Page\Model\Orm\Tree::UID_TITLE}
					{continueIf $item->uid === App\PostType\Page\Model\Orm\Tree::UID_ESHOP}
					{php $pathItems[] = $item}
				{/foreach}
				{php $pathItems[] = $categoryMain}
				{foreach $pathItems as $category}
					<a href="{plink $category}">{$category->name}</a>{if !$iterator->isLast()}{('angle-right-bold')|icon}{/if}
				{/foreach}
			</p>
		{/foreach}
	</nav>
{/if}
