{varType App\Model\BucketFilter\Box\CheckBoxes $box}
{var $isFlagFilter = isset($filter->boxesByNamespace['flags']) && in_array($box, $filter->boxesByNamespace['flags'])}

<div class="b-filters__group" n:if="$isFlagFilter">
	{capture $link}{link 'this', 'filter' => $box->filterToDeSelect, 'page' => null}{/capture}
	{php $link = urldecode(htmlspecialchars_decode($link))}
	<p class="b-filters__title">
		<strong>{$box->title}</strong>
		<a href="{$link}"{if isset($linkSeo) && $linkSeo->hasNofollow( $linkSeoPage ?? $object, ['filter' => $box->filterToDeSelect])} rel="nofollow"{/if} class="b-filters__remove" data-naja data-naja-loader="body">{_btn_filter_cancel}</a>
	</p>
</div>
<div class="b-filters__group" n:if="!$isFlagFilter" n:foreach="$box->getCheckedItems() as $value">
	{capture $link}{link 'this', filter => $value->filterToDeSelect, page => null}{/capture}
	{php $link = urldecode(htmlspecialchars_decode($link))}
	<p class="b-filters__title">
		{$box->title}:
		<strong>{$value->name} {if $box->unit}{$box->unit}{/if}</strong>
		<a href="{$link}"{if isset($linkSeo) && $linkSeo->hasNofollow( $linkSeoPage ?? $object, ['filter' => $value->filterToDeSelect])} rel="nofollow"{/if} class="b-filters__remove" data-naja data-naja-loader="body">
			{_btn_filter_cancel}
		</a>
	</p>
</div>
