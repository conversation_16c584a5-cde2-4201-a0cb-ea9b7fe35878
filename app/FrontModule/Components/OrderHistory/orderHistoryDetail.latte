{varType App\Model\Orm\Order\Order $order}

<p class="message message--error" n:if="$order === null">{_order_not_found}</p>

{if $order !== null}
	{foreach $flashes as $flash}
		<div class="message message-{$flash->type}">{_$flash->message}</div>
	{/foreach}

	{* Informace *}
	<div n:class="'b-order-detail tw-border-[0.1rem] tw-border-solid tw-border-tile tw-rounded-xl tw-p-[2rem] md:tw-p-[4rem] max-lg:tw-flex-col tw-flex tw-gap-[1.6rem] lg:tw-gap-[4rem]', $order->isPaid() ? 'tw-mb-[2.4rem] md:tw-mb-[3.2rem]', 'tw-mb-[1.2rem]'">
		<table class="b-order-detail__table tw-flex-1">
			{* Cena s DPH *}
			<tr>
				<th class="tw-w-[13.5rem] md:tw-w-[17rem]">{_'order_total_price'}</th>
				<td>{$order->getTotalPriceWithDelivery()|money} <span class="tw-text-help">({$order->getTotalPriceWithDeliveryVat()|money} {_"with_vat"})</span></td>
			</tr>

			{* Doprava a platba *}
			<tr>
				<th>{_'order_delivery_method'}</th>
				<td>
					{* {php $imgs = $order->delivery->deliveryMethod->cf->deliveryPayment??->icon ?? []}
					{foreach $imgs as $img}
						<img class="b-order-detail__icon" src="{$img->getSize('sm')->src}" alt="" loading="lazy" width="33" height="25">
					{/foreach} *}
					{$order->delivery->getName()}
				</td>
			</tr>
			<tr>
				<th>{_'order_payment_method'}</th>
				<td>{$order->payment?->getName()}</td>
			</tr>

			{* Adresa *}
			<tr>
				<th>{_'order_billing_address'}</th>
				<td>
					{implode(', ', array_filter([
						$order->companyName ?? false,
						$order->name,
						$order->street,
						$order->city . ', ' . $order->zip,
						($order->ic ?? false) ? $translator->translate("form_label_ic") . ': ' . $order->ic : false,
						($order->dic ?? false) ? $translator->translate("form_label_dic") . ': ' . $order->dic : false,
						(($address->phone ?? false)|phoneFormat)
					]))|noescape}
				</td>
			</tr>
			<tr>
				<th>{_'order_delivery_address'}</th>
				<td>
					{if empty($order->dCity)}
						{_'order_address_same_as_billing'}
					{else}
						{implode(', ', array_filter([
							$order->dCompany ?? false,
							$address->dFirstname . ' ' . $address->dLastname,
							$order->dStreet,
							$order->dCity . ', ' . $order->dZip,
							(($address->dPhone ?? false)|phoneFormat)
						]))|noescape}
					{/if}
				</td>
			</tr>

			{* Poznámka *}
			<tr n:if="$order->note">
				<th>{_'order_note'}</th>
				<td>{$order->note|breakLines|noescape}</td>
			</tr>
		</table>
		<p class="tw-mb-0 tw-border-tile tw-border-[0.1rem] tw-border-solid lg:tw-border-t-0 max-lg:tw-border-l-0 tw-border-r-0 tw-border-b-0 max-lg:tw-pt-[1.6rem] lg:tw-pl-[4rem] tw-flex-[0_0_auto] tw-flex tw-flex-col tw-gap-[1.2rem] tw-justify-center">
			{* Add all items to cart *}
			<a href="{plink User:default, do => 'repeatOrder', orderHash => $order->hash}" class="btn btn--block btn--lg">
				<span class="btn__text">
					{_"btn_order_again"}
				</span>
			</a>

			{* Order tracking button - if needed in the future *}
			{* <a href="#" target="_blank" rel="noopener noreferrer" class="btn btn--bd btn--block btn--lg">
				<span class="btn__text">
					{_"btn_watch_order"}
				</span>
			</a> *}
		</p>
	</div>

	{* Platba *}
	{if !$order->isPaid()}
		{var $paymentType = $order->payment->paymentMethod->getPaymentMethod()->getPaymentType()}
		{if $order->payment?->information instanceof App\Model\Orm\Order\Payment\BankTransferPaymentInformation}
			{* Bankovní převod *}
			<div class="tw-bg-status-invalid-light tw-rounded-md md:tw-rounded-xl tw-p-[2.4rem_1.6rem] md:tw-p-[4.8rem_6rem] tw-mb-[2.4rem] md:tw-mb-[3.2rem]">
				{include $templates.'/part/box/payment-instructions.latte'}

				{* <a n:href="changeToCardPayment!, hash: $order->hash" data-naja data-naja-loader="body" data-naja-history="off" class="btn">
					<span class="btn__text">
						{_'order_change_to_card_payment_btn'}
					</span>
				</a> *}
			</div>
		{elseif $order->payment?->information instanceof App\Model\Orm\Order\Payment\CardPaymentInformation}
			{* Platba online *}
			<a n:href="changeToBankPayment!, orderHash: $order->hash" data-naja data-naja-loader="body" data-naja-history="off" class="btn">
				<span class="btn__text">
					{_'order_change_to_bank_payment_btn'}
				</span>
			</a>
			<a n:if="$order->payment?->information->redirectUrl() ?? false" href="{$order->payment?->information->redirectUrl()}">
				{_'order_show_card_payment_btn'}
			</a>
		{elseif $order->payment?->information instanceof App\Model\Orm\Order\Payment\BenefitCardPaymentInformation}
			{* Platba benefit kartou *}
			<a n:if="$order->payment?->information->redirectUrl() ?? false" href="{$order->payment?->information->redirectUrl()}">
				{_'order_show_benefit_card_payment_btn'}
			</a>
		{/if}
	{/if}

	{* Objednávka, expedice, předání *}
	{define #step}
		{default $valid = false}
		{default $icon = false}
		{if $valid}{php $icon = 'check'}{/if}
		<span n:class="'tw-flex-1 tw-rounded-full tw-p-[1rem] tw-flex tw-justify-center', $valid ? 'tw-bg-status-valid-light tw-font-bold' : 'tw-bg-bg tw-text-help'">
			<span n:tag-if="$icon" class="item-icon">
				{if $icon}{($icon)|icon, 'item-icon__icon tw-text-status-valid'}{/if}
				<span n:tag-if="$icon" class="item-icon__text">
					{$content}
				</span>
			</span>
		</span>
	{/define}
{*
	<p class="tw-flex max-lg:tw-flex-col tw-text-[1.4rem] tw-gap-[0.4rem] lg:tw-gap-[1.6rem] tw-mb-[3.2rem]">
		{capture $content}{_"order_created"}: {$order->placedAt|date:'j. n. Y'}{/capture}
		{include #step, valid: true, content: $content}

		<span class="tw-relative tw-self-center tw-w-[0.1rem] lg:tw-w-[2.8rem] tw-h-[1.2rem] lg:tw-h-[0.1rem] tw-bg-text before:tw-content-[''] before:tw-border-solid before:tw-border-transparent max-lg:before:tw-border-t-text lg:before:tw-border-l-text before:tw-absolute max-lg:before:tw-bottom-[-0.1rem] lg:before:tw-right-[-0.1rem] max-lg:before:tw-left-[50%] lg:before:tw-top-[50%] max-lg:before:tw-translate-x-[-50%] lg:before:tw-translate-y-[-50%] before:tw-border-t-[0.5rem] before:tw-border-r-[0.3rem] before:tw-border-b-0 before:tw-border-l-[0.3rem] lg:before:tw-border-t-[0.3rem] lg:before:tw-border-r-0 lg:before:tw-border-b-[0.3rem] lg:before:tw-border-l-[0.5rem]"></span>

		{capture $content}{_"order_dispatched"}{/capture}
		{include #step, valid: true, content: $content}

		<span class="tw-relative tw-self-center tw-w-[0.1rem] lg:tw-w-[2.8rem] tw-h-[1.2rem] lg:tw-h-[0.1rem] tw-bg-text before:tw-content-[''] before:tw-border-solid before:tw-border-transparent max-lg:before:tw-border-t-text lg:before:tw-border-l-text before:tw-absolute max-lg:before:tw-bottom-[-0.1rem] lg:before:tw-right-[-0.1rem] max-lg:before:tw-left-[50%] lg:before:tw-top-[50%] max-lg:before:tw-translate-x-[-50%] lg:before:tw-translate-y-[-50%] before:tw-border-t-[0.5rem] before:tw-border-r-[0.3rem] before:tw-border-b-0 before:tw-border-l-[0.3rem] lg:before:tw-border-t-[0.3rem] lg:before:tw-border-r-0 lg:before:tw-border-b-[0.3rem] lg:before:tw-border-l-[0.5rem]"></span>

		{capture $content}{_"order_delivered"}: <span class="flag flag--red-light tw-text-[1.1rem] tw-min-h-[2.2rem]">Čeká na vyzednutí</span>{/capture}
		{include #step, content: $content}
	</p>
*}
	{* Dokumenty *}
	{*include $templates.'/part/crossroad/files.latte'*}

	{* Produkty *}
	{include $templates.'/part/crossroad/table-order-content.latte'}
{/if}

{* Kurzy *}
{* {if !$user->loggedIn}
	{include $templates.'/part/crossroad/table-courses.latte', title: $translator->translate('title_my_courses'), items: $order->getAllClasses()}
{/if} *}

{* <p n:if="$pages->reclamation ?? false" class="b-order-detail__title-btns u-mb-0">
	<a href="{plink $pages->reclamation}" class="btn btn--white btn--sm">
		<span class="btn__text">
			{$pages->reclamation->nameAnchor}
		</span>
	</a>
</p> *}

{* Historie změn *}
{* <h2>{_'order_history_title'}</h2>
<table>
	<tbody>
	<tr n:foreach="$order->stateChanges as $history">
		<td>
			{$history->changedAt|date:'d. m. Y H:i:s'}
		</td>
		<td>
			{if $history->from !== null}{_'order_status_'.$history->from->value}{/if}
		</td>
		<td>
			{_'order_status_'.$history->to->value}
		</td>
	</tr>
	</tbody>
</table> *}
