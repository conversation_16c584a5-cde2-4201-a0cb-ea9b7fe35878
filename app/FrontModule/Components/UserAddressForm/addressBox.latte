{default $nameSuffix = ''}

<div class="b-user-address">
	<div data-controller="address-suggest">
		{include '../inp.latte', form: $form, name: 'address_title'.$nameSuffix, required: true, validate: true, class: 'tw-mb-[1.2rem]', showError: true}
		{include '../inp.latte', form: $form, name: 'inv_firstname'.$nameSuffix, required: true, validate: true, class: 'tw-mb-[1.2rem]', showError: true}
		{include '../inp.latte', form: $form, name: 'inv_lastname'.$nameSuffix, required: true, validate: true, class: 'tw-mb-[1.2rem]', showError: true}
		{include '../inp.latte', form: $form, name: 'inv_phone'.$nameSuffix, type: 'tel'}
		{include '../inp.latte', form: $form, name: 'inv_street'.$nameSuffix, required: true, validate: true, inpClass: 'smartform-street-and-number', class: 'tw-mb-[1.2rem]', showError: true}
		{include '../inp.latte', form: $form, name: 'inv_city'.$nameSuffix, required: true, validate: true, inpClass: 'smartform-city', class: 'tw-mb-[1.2rem]', showError: true}

		<div class="tw-mb-[2.4rem]">
			<div class="grid">
				<div class="grid__cell size--4-12@sm">
					{include '../inp.latte', form: $form, name: 'inv_zip'.$nameSuffix, required: true, validate: true, inpClass: 'smartform-street-zip', class: 'tw-mb-0', showError: true}
				</div>
				<div class="grid__cell size--8-12@sm">
					{include '../inp.latte', form: $form, name: 'inv_state'.$nameSuffix, validate: true, class: 'tw-mb-0', showError: true}
				</div>
			</div>
		</div>
	</div>

	{* Chci nakoupit na firmu *}
	<div n:class="f-open, $form['companyTab' . $nameSuffix]->value ? is-open, 'tw-mb-[2.4rem]'" data-controller="toggle-class">
		<p class="tw-mb-[2.4rem]">
			<label class="inp-item inp-item--checkbox">
				{var $controlName = 'companyTab'.$nameSuffix}
				<input n:name="$controlName" value="1" class="inp-item__inp" data-action="change->toggle-class#toggleInp">
				<span class="inp-item__text">
					{_'title_company_info'}
				</span>
			</label>
		</p>
		<div class="f-open__box">
			{include '../inp.latte', form: $form, name: 'inv_company' . $nameSuffix, validate: true, class:'tw-mb-[1.2rem]', required: true, showError: true}
			{include '../inp.latte', form: $form, name: 'inv_ic' . $nameSuffix, validate: true, class: 'tw-mb-[1.2rem]', required: true, showError: true}
			{include '../inp.latte', form: $form, name: 'inv_dic' . $nameSuffix, validate: true, class: 'tw-mb-[1.2rem]', showError: true}
		</div>
	</div>

	{* Chci zboží doručit na jinou, než fakturační adresu *}
	<div n:class="f-open, $form['deliveryTab' . $nameSuffix]->value ? is-open, 'tw-mb-[2.4rem]'" data-controller="toggle-class">
		<p class="tw-mb-[2.4rem]">
			<label class="inp-item inp-item--checkbox">
				{var $controlName = 'deliveryTab'.$nameSuffix}
				<input n:name="$controlName" value="1" class="inp-item__inp" data-action="change->toggle-class#toggleInp">
				<span class="inp-item__text">
					{_'title_delivery_address_toggle'}
				</span>
			</label>
		</p>
		<div class="f-open__box" data-controller="address-suggest">
			<h3 class="h4 tw-mb-[1.6rem]">{_"personal_info"}</h3>
			{include '../inp.latte', form: $form, name: 'del_firstname' . $nameSuffix, validate: true, class:'tw-mb-[1.2rem]', required: true, showError: true}
			{include '../inp.latte', form: $form, name: 'del_lastname' . $nameSuffix, validate: true, class:'tw-mb-[1.2rem]', required: true, showError: true}
			{include '../inp.latte', form: $form, name: 'del_company' . $nameSuffix, validate: true, class:'tw-mb-[1.2rem]', required: true, showError: true}
			{include '../inp.latte', form: $form, name: 'del_phone' . $nameSuffix, type: 'tel', class: 'tw-mb-[1.2rem]', showError: true}
			{include '../inp.latte', form: $form, name: 'del_street' . $nameSuffix, validate: true, inpClass: 'smartform-street-and-number', class:'tw-mb-[1.2rem]', required: true, showError: true}
			{include '../inp.latte', form: $form, name: 'del_city' . $nameSuffix, validate: true, inpClass: 'smartform-city', class:'tw-mb-[1.2rem]', required: true}
			<div class="grid">
				<div class="grid__cell size--4-12@sm">
					{include '../inp.latte', form: $form, name: 'del_zip' . $nameSuffix, validate: true, inpClass: 'smartform-zip', class:'tw-mb-0', required: true}
				</div>
				<div class="grid__cell size--8-12@sm">
					{include '../inp.latte', form: $form, name: 'del_state' . $nameSuffix, class: 'tw-mb-0'}
				</div>
			</div>
		</div>
	</div>

	{* Poznámka *}
	{include '../inp.latte', class: 'tw-mb-[2.4rem]', form: $form, name: 'address_note' . $nameSuffix, rows: 3, placeholderLang: 'address_note_placeholder'}

	{if empty($nameSuffix)}{include '../inp.latte', class: 'tw-mb-[2.4rem]', form: $form, name: 'is_default' . $nameSuffix}{/if}

	<p class="tw-mb-0">
		<button class="btn btn--lg" n:name="save">
			<span class="btn__text">
				{_'btn_save_address'}
			</span>
		</button>
	</p>
</div>
