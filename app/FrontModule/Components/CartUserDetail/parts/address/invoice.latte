{default $nameSuffix = false}

<div data-controller="address-suggest">
	{include '../../../inp.latte', class: 'b-step2__inp', form: $form, name: inv_firstname . $nameSuffix, required: true, validate: true, showError: true}
	{include '../../../inp.latte', class: 'b-step2__inp', form: $form, name: inv_lastname . $nameSuffix, required: true, validate: true, showError: true}
	{include '../../../inp.latte', class: 'b-step2__inp', form: $form, name: inv_phone . $nameSuffix, required: true, validate: true, showError: true, type=>'tel'}

	{include '../../../inp.latte', class: 'b-step2__inp', form: $form, name: inv_street . $nameSuffix, validate: true, inpClass: 'smartform-street-and-number smartform-instance-inv'.$nameSuffix, required: true, showError: true, class: !$shoppingCart->useDeliveryAddress() ? 'u-d-n':'b-step2__inp', data: ['ares-target' => 'street']}
	{if isset($form['inv_countrycode'])}
		{include '../../../inp.latte', class: 'b-step2__inp', form: $form, name: inv_countrycode . $nameSuffix, required: true, validate: true, showError: true}
	{/if}

	<div class="grid">
		<div class="grid__cell size--7-12 size--8-12@sm">
			{include '../../../inp.latte', class: 'b-step2__inp', form: $form, name: inv_city . $nameSuffix, validate: true, inpClass: 'smartform-city smartform-instance-inv'.$nameSuffix, required: true, showError: true, class: !$shoppingCart->useDeliveryAddress() ? 'u-d-n':'b-step2__inp', data: ['ares-target' => 'city']} {* class: !$shoppingCart->useDeliveryAddress() ? 'u-d-n':''*}
		</div>
		<div class="grid__cell size--5-12 size--4-12@sm">
			{include '../../../inp.latte', class: 'b-step2__inp', form: $form, name: inv_zip . $nameSuffix, validate: true, inpClass: 'smartform-zip smartform-instance-inv'.$nameSuffix, required: true, showError: true, class: !$shoppingCart->useDeliveryAddress() ? 'u-d-n':'b-step2__inp', data: ['ares-target' => 'zip']}
		</div>
	</div>

	{include $templates.'/part/form/part/state.latte', name: 'inv_state'}
</div>

