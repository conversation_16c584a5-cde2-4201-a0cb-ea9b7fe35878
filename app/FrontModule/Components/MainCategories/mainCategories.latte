{varType Nextras\Orm\Collection\DbalCollection $categories}
{default $class = false}
{default $categoriesProductCount = []}

<section n:if="count($categories) > 0" n:ifcontent n:class="c-categories, $class" data-controller="toggle-class">
	<ul n:ifcontent class="c-categories__grid grid">
		{php $visibleCategoriesCount = 0}
		{foreach $categories as $c}
			{varType App\PostType\Page\Model\Orm\CatalogTree $c}
			<li n:class="c-categories__cell, grid__cell, 'size--6-12 size--4-12@md size--3-12@xl'" n:if="isset($categoriesProductCount[$c->id]) && $categoriesProductCount[$c->id] > 0">
				{php $visibleCategoriesCount++}
				{capture $link}{plink $presenter->getObject(), category: $c->alias->alias, page: null}{/capture}
				<a href="{$link}" class="c-categories__link">
					{$c->nameAnchor}
					{('arrow-right-bold')|icon}
				</a>
			</li>
		{/foreach}
	</ul>
	<p n:if="$visibleCategoriesCount > 8" class="c-categories__btn u-ta-c">
		<button type="button" class="as-link" data-action="toggle-class#toggle" aria-expanded="false" data-toggle-class="is-open">
			{_"btn_show_more_categories"} ({$visibleCategoriesCount - 8})
		</button>
	</p>
</section>
