<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CartFreeDelivery;

use App\FrontModule\Presenters\BasePresenter;

use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserProvider;
use App\Model\Orm\User\UserRepository;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\TranslatorDB;
use Brick\Money\Money;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter   $presenter
 */
final class CartFreeDelivery extends UI\Control
{

	private ?Money $freeDeliveryAmount = null;

	public function __construct(
		private readonly Mutation $mutation,
		private readonly State $state,
		private readonly PriceLevel $priceLevel,
		private readonly ShoppingCartInterface $shoppingCart,
		private readonly TranslatorDB $translator,
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
		private readonly UserProvider $userProvider,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->freeDeliveryAmount = $this->deliveryMethodConfigurationRepository->getFreeDeliveryAmount($this->mutation, $this->state, $this->priceLevel, $this->shoppingCart->getCurrency());
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->freeDeliveryAmount = $this->freeDeliveryAmount;
		$this->template->hasItemWithFreeDelivery = $this->shoppingCart->hasItemWithForcedFreeDelivery();
		$this->template->hasUserFreeDelivery = $this->hasUserfreeDelivery();
		$this->template->hasFreeDeliveryVoucher = $this->shoppingCart->hasFreeDeliveryVoucher();
		$this->template->show = $this->freeDeliveryAmount !== null;
		$this->template->pages = $this->mutation->pages;
		if ($this->template->show) {
			$this->freeDeliveryAmount = Price::from($this->freeDeliveryAmount)->asMoney();
			$this->template->cartAmount = Price::from($this->shoppingCart->getTotalPriceVat(includeGiftCertificates: false, precision: 2))->asMoney();
			$this->template->deltaAmount = $this->freeDeliveryAmount->minus($this->template->cartAmount);
			$this->template->isFree = $this->template->deltaAmount->isGreaterThan(0);
		}

		$this->template->render(__DIR__ . '/cartFreeDelivery.latte');
	}

	private function getUserEntity(): User|null
	{
		return $this->userProvider->userEntity;
	}

	private function hasUserfreeDelivery(): bool
	{
		if (!$userEntity = $this->getUserEntity()) {
			return false;
		}

		return $userEntity->freeTransit;
	}

}
