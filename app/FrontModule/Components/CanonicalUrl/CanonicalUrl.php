<?php

declare(strict_types = 1);

namespace App\FrontModule\Components\CanonicalUrl;

use App\FrontModule\Components\Robots\Robots;
use App\FrontModule\Presenters\Error\ErrorPresenter;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\Model\TranslatorDB;

/**
 * @property-read DefaultTemplate $template
 */
final class CanonicalUrl extends UI\Control
{

	private array $filter = [];
	private string $forcedUrl;

	private ?string $order = null;

	private bool $forceShowCannonical = false;

	public function __construct(
		private readonly mixed $object,
		private readonly Robots $robots,
		private readonly TranslatorDB $translator,
		private readonly LinkFactory $linkFactory,
	)
	{
	}

	public function render(): void
	{
		if ($this->getPresenter() instanceof ErrorPresenter) {
			return;
		}
		$this->getTemplate()->setTranslator($this->translator);

		$this->getTemplate()->showCanonical = $this->showCanonical();
		$this->getTemplate()->link = $this->forcedUrl ?? $this->getCanonicalForProduct() ?? $this->canonicalForPage() ?? $this->getDefaultLink();

		$this->template->render(__DIR__ . '/canonicalUrl.latte');
	}

	private function isCanonicalBanned(): bool
	{
		if ($this->robots->noFollowDamagedProduct()) {
			return true;
		}

		if ($this->robots->hasForceNoIndex()) {
			return true;
		}

		if ($this->robots->hasForceStateNoIndex()) {
			return true;
		}

		return false;
	}

	private function getCanonicalForProduct(): string|null
	{
		if (!($this->object instanceof ProductLocalization)) {
			return null;
		}

		if (!$this->object->product->isDamaged) {
			return null;
		}

		if ($this->object->product->damageLevel <= 1) {
			return null;
		}

		if (!$this->object->product->damagedParent) {
			return null;
		}

		$damagedParent = $this->getDamagedParentProduct();

		try {
			return $this->linkFactory->linkTranslateToNette($damagedParent->getLocalization($this->object->mutation));
		} catch (\Throwable $exception) {
			// try to generate link
		}

		return null;
	}

	private function getDamagedParentProduct(): Product
	{
		/**
		 * @var Product $product
		 */
		foreach ($this->object->product->damagedParent->damagedProducts as $product) {

			if ($product->damageLevel !== 1) {
				continue;
			}

			return $product;
		}

		return $this->object->product->damagedParent;
	}

	private function showCanonical(): bool
	{
		if ($this->forceShowCannonical) {
			return true;
		}

		if ($this->object instanceof CatalogTree) {
			if ($this->object->forceNoIndex) {
				return false;
			}
		}

		if ($this->isCanonicalBanned()) {
			return false;
		}

		return true;
	}

	public function setFilter(array $filter): void
	{
		$this->filter = $filter;
	}

	public function setOrder(?string $order): void
	{
		$this->order = $order;
	}

	private function canonicalForPage(): ?string
	{
		if ($this->object instanceof Tree && $this->object->uid === Tree::UID_SEARCH) {
			try {
				return $this->linkFactory->linkTranslateToNette($this->object);
			} catch (\Throwable $exception) {
				// try to generate link
			}
		}

		return null;
	}

	public function getDefaultLink(): string
	{
		return $this->linkFactory->linkTranslateToNette($this->object, ['filter' => $this->filter, 'order' => $this->order]);
	}

	public function setForceShowCanonical(bool $forceShowCannonical = true): void
	{
		$this->forceShowCannonical = $forceShowCannonical;
	}


	public function setForcedUrl(string $forcedUrl): void
	{
		$this->forcedUrl = $forcedUrl;
	}
}
