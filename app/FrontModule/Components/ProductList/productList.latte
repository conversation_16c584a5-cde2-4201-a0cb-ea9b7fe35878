{snippet productList}
	{default $class = $param['class'] ?? 'u-mb-md u-mb-2xl@md'}
	<section
			data-controller="gtm-viewport{if !count($products)} intersection{/if}"
			data-gtm-viewport-script-id-value="{$control->getName()}"
			data-gtm-viewport-item-class-value="b-product"
			data-gtm-viewport-item-event-value="{$gtmItemEvent}"
			n:ifcontent n:if="count($products) || $preInit" n:class="section, $class, block-loader
	">
		{capture $titleBlock}
			<h2 {if $param['id'] ?? false}id="{$param['id']}"{/if} n:if="$showTitle" class="section__title">{_$title|replace:array_keys($titleParameters),array_values($titleParameters)|noescape}</h2>
		{/capture}

		{if count($products)}
			{$titleBlock}
			<div class="section__carousel embla" data-controller="embla">
				<div class="embla__holder">
					<div class="embla__viewport" data-embla-target="viewport">
						<div class="section__grid grid grid--x-0 grid--y-0 grid--scroll embla__container">
							<div n:foreach="$products as $product" class="section__cell grid__cell">
								{control 'productBox-' . $product->id}
							</div>
						</div>
					</div>
				</div>
				<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
					{('arrow-left')|icon}
					<span class="u-vhide">{_btn_prev}</span>
				</button>
				<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
					{('arrow-right')|icon}
					<span class="u-vhide">{_btn_next}</span>
				</button>
			</div>
		{elseif $preInit}
			{$titleBlock}
			<div class="block-loader is-loading">
				<p class="u-vhide">
					<a n:href="init!" data-naja data-naja-history="off" data-naja-unique="off" data-intersection-target="btn">
						{_"btn_show"}
					</a>
				</p>
				<div class="section__placeholder"></div>
				<div class="block-loader__loader"></div>
			</div>
		{/if}
	</section>
{/snippet}

