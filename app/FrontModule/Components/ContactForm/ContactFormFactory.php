<?php

declare(strict_types=1);

namespace App\FrontModule\Components\ContactForm;

use App\Model\Email\CommonFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\PostType\ContactMessage\Model\Orm\ContactMessage\ContactMessage;
use Nette\Application\UI\Form as UIForm;
use Nette\Forms\Form;
use Nette\Utils\ArrayHash;
use Throwable;
use Tracy\Debugger;

final readonly class ContactFormFactory
{
	public function __construct(
		private CommonFormFactory $formFactory,
		private MutationHolder $mutationHolder,
		private LinkFactory $linkFactory,
		private CommonFactory $commonEmailFactory,
		private Orm $orm,
	) {}

	public function create(
		Routable $object,
		bool $standalone = false,
	): Form
	{
		$form = $this->formFactory->create($standalone ? Form::class : UIForm::class);

		$form->addText('name', 'form_label_name')
			->setRequired('');
		$form->addEmail('email', 'form_label_email')
			->setRequired();
		$form->addText('phoneNumber', 'form_label_phone_number');
		$form->addTextArea('text', 'form_label_text')
			->setRequired();

		$form->addSubmit('send');

		$form->onSuccess[] = function (Form $form, ArrayHash $values) use ($object): void {
			$values->type = 'contact';
			$values->page = $object->getNameTitle();
			$replyTo = [$values->email, $values->name];

			try {
				$values->pageLink = $this->linkFactory->linkTranslateToNette($object);
				$this->commonEmailFactory->create()
					->send(
						'',
						$this->mutationHolder->getMutation()->getRealAdminEmail(),
						'contact',
						(array)$values,
						null,
						$replyTo,
					);

				/** Save $contactMessage to database */
				$contactMessage = new ContactMessage();
				$contactMessage->type = $values->type;
				$contactMessage->email = $values->email;
				$contactMessage->phoneNumber = $values->phoneNumber ?? null;
				$contactMessage->name = $values->name ?? null;
				$contactMessage->text = $values->text;
				$contactMessage->createdAt = new \DateTimeImmutable();

				$this->orm->persistAndFlush($contactMessage);

			} catch (Throwable $error) {
				bdump($error);
				Debugger::log($error);
				$form->addError('Operation failed');
			}
		};

		return $form;
	}
}
