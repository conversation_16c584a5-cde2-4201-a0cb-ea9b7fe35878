{varType App\Model\Pages $pages}

{php $isSimple = isset($object->uid) && in_array($object->uid, ['step1', 'step2'])}
{var $msgs = $systemMessages??[]} {* Nejstarší message *}

{php $catalogHeaderImage = isset($object->cf->catalog_header->image) ? $object->cf->catalog_header->image->getEntity() ?? false : false}
{php $headerImage = isset($object->cf->header->image) ? $object->cf->header->image->getEntity() ?? false : false}
{php $hasHeaderImage = $catalogHeaderImage || $headerImage}

{php $isFixed = in_array($object->template, [':Front:Page:academy', ':Front:Homepage:default', ':Blog:Front:Blog:default', ':Front:Page:about'])} {* || $hasHeaderImage *}
{php $isInversed = in_array($object->template, [':Front:Page:academy', ':Front:Homepage:default', ':Front:Page:about']) || $hasHeaderImage}

<header n:class="header, $isSimple ? header--simple, $isFixed ? header--fixed, $isInversed ? header--inversed" data-controller="header etarget">
	<div class="header__main">
		<div class="row-main">
			<div class="header__inner">
				<div class="header__top">
					{if !$isSimple}
						{cache cacheKey('serviceMenu', $mutation), expire: '12 hour', tags: ['serviceMenu']}
							{control serviceMenu}
						{/cache}
					{/if}
					<div n:tag-if="!$isSimple" class="header__center">
						{include $templates.'/part/box/person-header.latte', class: 'header__contact'}
						{if !$isSimple}
							{control suggest}
						{/if}
					</div>
					<div class="header__right">
						{include './part/login.latte', class: 'header__login'}
						{include './part/basket.latte', class: 'header__basket'}
					</div>
				</div>
				<div class="header__bottom">
					<h1 n:tag="!$isHomepage ? 'p'" class="header__logo u-mb-0">
						<a href="{plink $pages->title}" n:tag-if="$pages->title !== null" class="prefetch">
							{include $templates.'/part/core/logo.latte', class: 'header__logo-svg'}
						</a>
					</h1>
					<div n:if="!$isSimple" class="header__menu-wrap overlay-pseudo">
						<nav class="header__menu m-main" id="menu-main" data-header-target="holder">
							<div class="m-main__holder">
								{var $cacheObject = ($object::class === App\PostType\Page\Model\Orm\CatalogTree::class) ? $object : 'default'}
								{cache cacheKey('mainMenu', $cacheObject, $mutation), tags: [App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization::class, App\PostType\Page\Model\Orm\Tree::class]}
									{control menu}
								{/cache}

								{* Servisní menu pro mobil *}
								{cache cacheKey('serviceMenu', $mutation), expire: '12 hour', tags: ['serviceMenu']}
									{control serviceMenu}
								{/cache}
							</div>
						</nav>
						<p class="header__burger u-mb-0">
							<button type="button" class="b-burger" data-action="header#toggleMenu" aria-expanded="false" data-header-target="menuToggle">
								<span class="b-burger__inner">
									<span></span>
									<span></span>
									<span></span>
									<span></span>
								</span>
								{_"menu"}
							</button>
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div n:ifcontent class="header__msg u-mb-last-0">
		<div n:ifcontent class="row-main">
			{* {control currencyDetector} *}
			{foreach $msgs as $msg}
			{include './part/infobar.latte', msg=>$msg}
			{/foreach}
		</div>
	</div>
</header>
