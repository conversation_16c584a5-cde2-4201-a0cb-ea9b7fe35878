<?php

declare(strict_types = 1);

namespace App\FrontModule\Components\GoogleLogin;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Google\GoogleProviderFactory;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Security\Acl;
use App\Model\TranslatorDB;
use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Provider\GoogleUser;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;
use League\OAuth2\Client\Token\AccessToken;
use Nette\Application\Attributes\CrossOrigin;
use Nette\Application\UI\Control;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Http\UrlScript;
use Nette\Security\SimpleIdentity;
use function assert;

/**
 * @property-read BasePresenter $presenter
 */
final class GoogleLogin extends Control
{

	private readonly SessionSection $session;

	public function __construct(
		private readonly bool $isEnabled,
		private readonly Mutation $mutation,
		private readonly TranslatorDB $translator,
		private readonly GoogleProviderFactory $googleProviderFactory,
		private readonly Orm $orm,
		private readonly UserModel $userModel,
		private readonly MessageForFormFactory $messageForFormFactory,
		Session $session,
	)
	{
		$this->session = $session->getSection(Google::class);
	}

	public function handleLogin(): void
	{
		if (!$this->isEnabled) {
			return;
		}

		$google = $this->googleProviderFactory->create($this->mutation);
		$componentPathParts = explode('-', $this->lookupPath());

		$thisLink = $this->presenter->link('//this');
		$redirectUri = (new UrlScript(
			$this->presenter->link($this->mutation->pages->userLogin, [LinkFactory::ABSOLUTE_LINK => true]))
		)->withQuery(
			(new UrlScript($this->presenter->netteLink(implode(':', $componentPathParts) . ':response!')))->getQuery()
		);
		$authorizationUrl = $google->getAuthorizationUrl([
			'redirect_uri' => (string) $redirectUri,
		]);

		$this->presenter->getHttpResponse()->setCookie('redirectToLink', $thisLink, '+10 minute');
		$this->session->state = $google->getState();
		$this->presenter->redirectUrl($authorizationUrl);
	}

	#[CrossOrigin]
	public function handleResponse(): void
	{
		$this->verifyState();

		$googleUser = $this->getUserData();

		$logged = $this->login($googleUser);
		if (!$logged) {
			$logged = $this->pairLoggedByEmail($googleUser);
		}
		if (!$logged) {
			$logged = $this->register($googleUser);;
		}
		if (!$logged) {
			$this->getPresenter()->flashMessage('msg_info_google_login_process_failed', 'error');
		}
		$redirectToLink = $this->presenter->getHttpRequest()->getCookie('redirectToLink');
		if ($redirectToLink !== '') {
			$this->presenter->redirectUrl($redirectToLink);
		}
		$this->getPresenter()->redirect('this');
	}

	private function verifyState(): void
	{
		$error = $this->getPresenter()->getParameter('error');
		if ($error !== null) {
			$this->getPresenter()->flashMessage('msg_info_google_login_verification_failed', 'error');
			$this->getPresenter()->redirect('this');
		}

		$state = $this->presenter->getParameter('state');
		if ($state === null || $this->session->state === null || !hash_equals($this->session->state, $state)) {
			$this->getPresenter()->redirect('this');
		}

		unset($this->session->state);
	}

	private function pairLoggedByEmail(GoogleUser $googleUser): bool
	{
		$userByEmail = $this->orm->user->getByEmail($googleUser->getEmail(), $this->mutation);

		if ($userByEmail === null) {
			return false;
		}

		if ($userByEmail->googleId === null) {
			$userByEmail->googleId = $googleUser->getId();
			$this->orm->user->persistAndFlush($userByEmail);
		}

		$this->presenter->getUser()->login(new SimpleIdentity($userByEmail->id, $userByEmail->role));
		return true;
	}

	private function login(GoogleUser $googleUser): bool
	{
		$googleId = $googleUser->getId();
		$userById = $this->orm->user->getBy([
			'googleId' => $googleId,
			'mutations->id' => $this->mutation->id,
		]);

		if ($userById === null) {
			return false;
		}

		$this->presenter->getUser()->login(new SimpleIdentity($userById->getPersistedId(), $userById->role));
		return true;
	}

	private function register(GoogleUser $googleUser): bool
	{
		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = PriceLevel::DEFAULT_ID;
		$user->mutations->add($this->mutation);

		$user = $this->userModel->save($user, [
			'email' => $googleUser->getEmail(),
			'googleId' => $googleUser->getId(),
		]);

		$this->getPresenter()->getHttpResponse()->setCookie('registrationComplete', '1', '+1 minute');
		$this->getPresenter()->getUser()->login(new SimpleIdentity($user->id, $user->role));

		return true;
	}

	private function getUserData(): GoogleUser
	{
		$google = $this->googleProviderFactory->create($this->mutation);

		$accessToken = $google->getAccessToken('authorization_code', [
			'code' => $this->presenter->getParameter('code'),
			'redirect_uri' => $this->link('//response!'),
		]);

		assert($accessToken instanceof AccessToken);

		try {
			$googleUser = $google->getResourceOwner($accessToken);
			assert($googleUser instanceof GoogleUser);
		} catch (\Throwable) {
			$this->getPresenter()->flashMessage('msg_info_google_load_data_failed', 'error');
			$this->getPresenter()->redirect('this');
		}

		return $googleUser;
	}

	public function render(): void
	{
		if (!$this->isEnabled) {
			return;
		}

		$this->template->setTranslator($this->translator);
		$this->template->mutation = $this->mutation;
		$this->template->render(__DIR__ . '/GoogleLogin.latte');
	}

	public function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
