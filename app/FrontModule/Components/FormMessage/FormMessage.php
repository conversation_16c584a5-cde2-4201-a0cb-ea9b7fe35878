<?php

declare(strict_types=1);

namespace App\FrontModule\Components\FormMessage;


use App\FrontModule\Presenters\BasePresenter;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use stdClass;

/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 * @property-read UI\Control $parent
 */
final class FormMessage extends UI\Control
{

	private array $messages = [];
	public function __construct(
		private readonly TranslatorDB $translator,
	)
	{
	}

	public function render(array $flashes = [], ?UI\Form $form = null, bool $onlyFirst = true, mixed $data = null, ?stdClass $stdMessage = null): void
	{
		$this->template->setTranslator($this->translator);

		$this->processMessage($flashes, $form, $stdMessage);

		$this->template->msgs = $this->messages;
		$this->template->onlyFirst = $onlyFirst;
		$this->template->data = $data ?? null;

		$this->template->render(__DIR__ . '/formMessage.latte');
	}

	private function processMessage(array $flashes = [], ?UI\Form $form = null, ?stdClass $stdMessage = null): void
	{
		$messages = [];
		if ($form && $form->hasErrors()) {
			// tyto hlasky jiz byvaji prelozene
			foreach ($form->getErrors() as $error) {
				$messages[$error] = $error;
				$this->addMessage($error, 'error');
			}
		}

		foreach ($flashes as $flash) {
			//tyto prelozene nejsou -> je nutne je prohnat translatorem
			if (!isset($messages[$flash->message])) {
				// zjisteni zda hlaska neobsahuje odkaz k vlozeni
				$link = null;
				if (strpos($flash->message, "::") !== false) {
					$tmp            = explode("::", $flash->message);
					$flash->message = $tmp[0];
					$link           = $tmp[1];
				}

				$strTranslated = $this->translator->translate($flash->message);

				// nahrazeni pripadneho odkazu
				if ($link && strpos($strTranslated, "%link%") !== false) {
					$strTranslated = str_replace("%link%", $link, $strTranslated);
				}

				$this->addMessage($strTranslated, $flash->type);
				$messages[$flash->message] = $flash->message;
			}
		}

		if ($stdMessage) {
			$this->addMessage($stdMessage->text, $stdMessage->type);
		}
	}
	private function addMessage(string $msg, string $type): void
	{
		$tmp = new stdClass();
		$tmp->text = $msg;
		$tmp->type = $type;
		$this->messages[] = $tmp;
	}
}
