<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Parameter\Components\ValueEditForm;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\Form\Form;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;

final class ValueEditForm extends UI\Control
{

	public function __construct(
		private readonly ParameterValue $parameterValue,
		private readonly Translator $translator,
		private readonly ParameterValueModel $parameterValueModel,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->parameterValue = $this->parameterValue;
		$this->template->corePartsDirectory = Form::TEMPLATE_PARTS_DIR;
		$this->template->templates = RS_TEMPLATE_DIR;
		$this->template->fileUploadLink = $this->presenter->link(':Admin:File:upload');
		$this->template->mutation = $this->mutationsHolder->getDefaultRs();

		$this->template->render(__DIR__ . '/valueEditForm.latte');
	}

	public function renderSide(): void
	{
		$this->template->render(__DIR__ . '/side.latte');
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$setup = $form->addContainer('setup');
		$setup->addHidden('cf');

		$form->addSubmit('send');
		$form->onSuccess[] = [$this, 'editFormSucceeded'];
		return $form;
	}

	public function editFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = (array) $form->getHttpData();
		$this->parameterValueModel->saveValueDetail($this->parameterValue, $valuesAll);
	}

}
