{var $anchorName = 'content'}
{var $icon = $templates . '/part/icons/cog.svg'}
{var $title = 'Nastavení'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}

{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{varType App\Model\Orm\User\User $userEntity}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['name'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['publicFrom'],
			type: 'date',
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['publicTo'],
			type: 'date',
			classesLabel: ['title'],
		]}
	{/block}
{/embed}
