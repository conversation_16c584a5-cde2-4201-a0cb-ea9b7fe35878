<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\PaymentMethod\Components\Form;

use App\AdminModule\Presenters\PaymentMethod\Components\Form\FormData\BaseFormData;
use App\Model\CustomField\CustomFields;
use App\Model\Orm\Orm;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\PaymentMethod\PaymentMethodConfigurationRepository;
use App\Model\Orm\PaymentMethod\PaymentMethodCurrency;
use App\Model\Orm\PaymentMethod\PaymentMethodPrice;
use App\Model\Orm\Price;
use App\Model\Orm\User\User;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;

final readonly class Handler
{

	public function __construct(
		private Orm $orm,
		private PaymentMethodConfigurationRepository $paymentMethodConfigurationRepository,
		private CustomFields $customFields,
	)
	{
	}

	public function handle(PaymentMethodConfiguration $paymentMethodConfiguration, BaseFormData $data, User $user): void
	{
		$paymentMethodConfiguration->public = $data->publish->public;
		$paymentMethodConfiguration->name = $data->name;
		$paymentMethodConfiguration->desc = $data->desc;
		$paymentMethodConfiguration->pageText = $data->pageText;
		$paymentMethodConfiguration->tooltip = $data->tooltip;
		$paymentMethodConfiguration->sort = $data->sort;
		$paymentMethodConfiguration->isRecommended = $data->isRecommended;
		$paymentMethodConfiguration->pageShow = $data->pageShow;
		$paymentMethodConfiguration->setCf($this->customFields->prepareDataToSave($data->cf));

		$this->handleCurrencies($paymentMethodConfiguration, $data);
		$this->handleVats($paymentMethodConfiguration, $data);
		$this->handlePrices($paymentMethodConfiguration, $data);

		$this->paymentMethodConfigurationRepository->persistAndFlush($paymentMethodConfiguration);
	}

	private function handleCurrencies(PaymentMethodConfiguration $paymentMethodConfiguration, BaseFormData $data): void
	{
		$toDelete = $paymentMethodConfiguration->currencies->toCollection()->fetchPairs('currency');
		$currencies = $data->currencies;

		foreach ($currencies as $currency) {
			if (!isset($toDelete[$currency])) {
				$c = new PaymentMethodCurrency();
				$c->currency = $currency;
				$paymentMethodConfiguration->currencies->add($c);
			}
			unset($toDelete[$currency]);
		}

		$this->paymentMethodConfigurationRepository->persistAndFlush($paymentMethodConfiguration);

		foreach ($paymentMethodConfiguration->prices->toCollection()->findBy(['price->currency' => array_keys($toDelete)]) as $price) {
			$this->orm->paymentMethodPrice->remove($price);
		}

		/** @var PaymentMethodCurrency $delete */
		foreach ($toDelete as $delete) {
			$this->orm->paymentMethodCurrency->remove($delete);
		}
		$this->orm->flush();
	}

	private function handleVats(PaymentMethodConfiguration $paymentMethodConfiguration, BaseFormData $data): void
	{
		if (isset($data->vats)) {
			$vats = [];
			foreach ($data->vats as $stateId => $vatData) {
				$vats[$stateId] = $vatData->vatRate;
			}

			$paymentMethodConfiguration->vats = $vats;
		}
	}

	private function handlePrices(PaymentMethodConfiguration $paymentMethodConfiguration, BaseFormData $data): void
	{
		$tmpIds = [];

		if (isset($data->prices)) {
			//update items

			foreach ($data->prices as $priceId => $priceData) {
				if ($priceId === 'newItemMarker') {
					continue;
				}

				if (is_int($priceId)) {
					$price = $this->orm->paymentMethodPrice->getById($priceId);
				} else {
					$price = new PaymentMethodPrice();
					$price->paymentMethod = $paymentMethodConfiguration;
				}

				if ($price === null) {
					continue;
				}

				$this->handleOnePrice($price, $priceData);

				$this->orm->paymentMethodPrice->persistAndFlush($price);
				$tmpIds[] = $price->id;
			}

		}

		/** @var PaymentMethodPrice $price */
		foreach ($paymentMethodConfiguration->prices as $price) {
			if (!in_array($price->id, $tmpIds)) {
				$this->orm->paymentMethodPrice->remove($price);
			}
		}
	}

	private function handleOnePrice(PaymentMethodPrice $price, ArrayHash $data): void
	{
		$price->priceLevel = $data->priceLevel;
		$price->state = $data->state;
		$price->price = Price::from(Money::of($data->price, $data->currency));
		$price->externalId = $data->externalId;
	}

}
