<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\State\Components\DataGrid;

use App\Model\Orm\Orm;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use LogicException;
use Nette\Application\UI\Control;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{


	public function __construct(
		private readonly Translator $translator,
		private readonly StateRepository $stateRepository,

	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->stateRepository->findAll());



		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();
		$grid->addColumnText('code', 'code')->setSortable()->setFilterText();
		$grid->addColumnText('vatRates', 'state_label_vatRate')->setRenderer(
			function (State $state) {
				$parts = [];
				foreach ($state->vatRates as $vatRate=>$vatValue) {
					if ($vatValue !== null) {
						$parts[] = sprintf('%s%% (%s)', $vatValue, $this->translator->translate('vat_rate_') . $vatRate->value);
					}

				}
				return implode(', ', $parts);
			}
		);
		$grid->addColumnStatus('public', 'public')
			->addOption(1, 'public')->setIcon('check')->endOption()
			->addOption(0, 'non-public')->setClass('btn-danger')->endOption()
			->onChange[] = [$this, 'publicChange']
		;



		$grid->setTranslator($this->translator);



		return $grid;
	}

	public function publicChange(string $id, string $public): void
	{
		$state = $this->stateRepository->getByIdChecked((int)$id);
		$state->public = (int)$public;
		$this->stateRepository->persistAndFlush($state);

		if ($this->presenter->isAjax()) {
			$this['grid']->redrawItem($id);
		}
	}

}
