<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Voucher\Components\Form;

use App\AdminModule\Presenters\Voucher\Components\Form\FormData\BaseFormData;
use App\Model\Orm\User\User;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\Voucher\VoucherRepository;
use App\Model\Orm\VoucherCode\VoucherCodeModel;
use Brick\Math\BigDecimal;
use Nette\Forms\Controls\SubmitButton;
use Nextras\Dbal\Utils\DateTimeImmutable;

final readonly class Handler
{

	public function __construct(
		private VoucherRepository $voucherRepository,
		private VoucherCodeModel $voucherCodeModel,
	)
	{
	}

	public function handle(\Nette\Application\UI\Form $form, Voucher $voucher, BaseFormData $data, User $user): void
	{
		if ($data->internalName === null) {
			$voucher->name = $data->name;
		} else {
			$this->handleCommon($voucher, $data, $user);
		}

		$this->handleCodes($form, $voucher, $data, $user);
		$this->voucherRepository->persistAndFlush($voucher);
	}

	private function handleCommon(Voucher $voucher, BaseFormData $commonFormData, User $user): void
	{
		$voucher->edited = $user->id;
		$voucher->editedTime = new DateTimeImmutable();

		$voucher->public = (int) $commonFormData->publish->public;
		$voucher->publicFrom = $commonFormData->publicFrom ?? (new DateTimeImmutable())->setTime((int) date('H'), 0);
		$voucher->publicTo = $commonFormData->publicTo ?? (new DateTimeImmutable())->modify('+100 year')->setTime((int) date('H'), 0);
		$voucher->minPriceOrder = $commonFormData->minPriceOrder !== null ? BigDecimal::of($commonFormData->minPriceOrder) : null;
		$voucher->name = $commonFormData->name;
		$voucher->internalName = $commonFormData->internalName;
		$voucher->reuse = (int) $commonFormData->reuse;
		$voucher->combination = (int) $commonFormData->combination;
		$voucher->combinationType = $commonFormData->combinationType;

		$voucher->discount = $commonFormData->discount;
		$voucher->discountPercent = $commonFormData->discountPercent;
	}

	private function handleCodes(\Nette\Application\UI\Form $form, Voucher $voucher, BaseFormData $values, User $user): void
	{
		if ($form->isSubmitted() instanceof SubmitButton && $form->isSubmitted()->getName() === 'generate') {
			if (isset($values->codes['newVoucherCount']) && $values->codes['newVoucherCount']) {
				$this->voucherCodeModel->addCodes($voucher, $values->codes['newVoucherCount'], $values->codes['newVoucherPrefix']);
			}

			if (isset($values->codes['newVoucherString']) && $values->codes['newVoucherString']) {
				$this->voucherCodeModel->addExactCode($voucher, $values->codes['newVoucherString']);
			}
		}
	}

}
