{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
{varType App\Model\Orm\Product\Product $product}
<div class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				hrefClose: $presenter->link('Catalog:class'),
				title: $product->name,
				hasGeneratedMenu: true,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">
		<div class="message message--{$flash->type} u-mb-sm" n:foreach="$flashes as $flash">
			{$flash->message}
		</div>

		{control classForm}

		{var $props = [
			title: 'Harmonogram školení',
			id: 'class-section',
			icon: $templates.'/part/icons/align-justify.svg',
			open: true,
			variant: 'main',
			classes: ['u-mb-xxs'],
			rowMainClass: 'row-main-max',
		]}
		{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
			{block content}
				{control sectionDataGrid}
			{/block}
		{/embed}

		{var $props = [
			title: 'Termíny školení',
			id: 'class-event',
			icon: $templates.'/part/icons/calendar-alt.svg',
			open: true,
			variant: 'main',
			classes: ['u-mb-xxs'],
			rowMainClass: 'row-main-max',
		]}
		{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
			{block content}
				{control eventDataGrid}
			{/block}
		{/embed}
	</div>


	<div class="main__content-side scroll">
		{control classForm:side}
	</div>
</div>



