<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Library;

use App\AdminModule\Components\Tree\Tree;
use App\AdminModule\Components\Tree\TreeFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Library\Components\ImageDetailForm\ImageDetailForm;
use App\AdminModule\Presenters\Library\Components\ImageDetailForm\ImageDetailFormFactory;
use App\AdminModule\Presenters\Library\Components\LibraryFilter\LibraryFilter;
use App\AdminModule\Presenters\Library\Components\LibraryFilter\LibraryFilterFactory;
use App\AdminModule\Presenters\Library\Components\LibraryForm\LibraryForm;
use App\AdminModule\Presenters\Library\Components\LibraryForm\LibraryFormFactory;
use App\Model\Image\Storage\BasicStorage;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\LibraryImage\LibraryImageModel;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use App\Model\Orm\LibraryTree\LibraryTree;
use App\Model\Orm\LibraryTree\LibraryTreeModel;
use App\Model\Orm\LibraryTree\LibraryTreeRepository;
use App\Model\TreeStructure\CommonTreeStructure;
use App\Model\TreeStructure\MovingModel;
use LogicException;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Responses\FileResponse;
use Nette\Http\FileUpload;
use Nette\Http\SessionSection;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class LibraryPresenter extends BasePresenter
{

	private const DEFAULT_LIBRARY_ID = 1;

	#[Persistent]
	public int $thickbox = 0;

	public ?\App\Model\Orm\LibraryTree\LibraryTree $forcedLibraryTree = null;

	private ?SessionSection $filterSession = null;

	protected \App\Model\Orm\LibraryTree\LibraryTree $object;

	public ?int $forcedPage = null;
	private LibraryImage $libraryImage;

	public function __construct(
		private readonly LibraryFilterFactory $libraryFilterFactory,
		private readonly LibraryFormFactory $libraryFormFactory,
		private readonly LibraryImageModel $libraryImageModel,
		private readonly LibraryTreeModel $libraryTreeModel,
		private readonly LibraryTreeRepository $libraryTreeRepository,
		private readonly TreeFactory $treeFactory,
		private readonly MovingModel $treeMovingModel,
		private readonly BasicStorage $imageStorage,
		private readonly LibraryImageRepository $libraryImageRepository,
		private readonly ImageDetailFormFactory $imageDetailFormFactory,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();

		$this->addComponent($this->visualPaginatorFactory->create(true), 'pager');
		$this->filterSession = $this->session->getSection(LibraryFilter::LIBRARY_FILTER_SESSION_NAME);
		if ($this->filterSession->data === null) {
			$this->filterSession->data = [];
		}
	}
	public function actionDefault(?int $id = self::DEFAULT_LIBRARY_ID, ?int $thickbox = 0): void
	{
		$this->thickbox = $thickbox;

		if ($id === null || ($object = $this->libraryTreeRepository->getById($id)) === null) {
			$libraryRoot = $this->libraryTreeRepository->getBy(['parent' => null]);
			if ($libraryRoot !== null) {
				$this->redirect('default', ['id' => $libraryRoot->id]);
			} else {
				throw new LogicException('Missing Library root page');
			}
		} else {
			$this->object = $object;
		}
	}


	public function renderDefault(): void
	{
		$this->detectFirstPageEnforcer();

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->getParam('adminImagePaging');

		if ($this->filterSession->data !== []) {
			$images = $this->orm->libraryImage->findByFilter($this->filterSession->data, $this->object);
		} else {
			$images = $this->object->images->toCollection();
		}

		if ($this->forcedPage !== null) {
			$paginator->setPage($this->forcedPage);
		}

		$paginator->itemCount = $images->countStored();
		$this->template->images = $images->limitBy($paginator->itemsPerPage, $paginator->offset)
			->orderBy('sort')
			->orderBy('name');

		$this->template->canDeleteImages = $this->user->isDeveloper();
		$this->template->filterData = $this->filterSession->data;

		$this->template->thickbox = $this->thickbox;
		$this->template->isAjax = $this->isAjax();
		$this->template->hasAdd = $this->imageStorage->hasImageActions();
		$this->template->hasItemActions = $this->imageStorage->hasImageActions();

		if ($this->thickbox === 1) {
			$this->setView('part/content');
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function actionImageOverlay(int $imageId): void
	{
		$this->libraryImage = $this->libraryImageRepository->getByIdChecked($imageId);
	}
	public function renderImageOverlay(): void
	{
		$this->template->add('libraryImage', $this->libraryImage);
		if ($this->isAjax()) {
			$this->redrawControl('imageMainForm');
		}
	}


	public function handleDeleteImage(int $imageId): void
	{
		$image = $this->orm->libraryImage->getById($imageId);
		if ($image !== null && ($libraryFolder = $image->library) !== null) {
			$libraryFolderId = $libraryFolder->id;
		} else {
			$libraryRoot = $this->libraryTreeRepository->getByChecked(['parent' => null]);
			$libraryFolderId = $libraryRoot->id;
		}

		if ($image !== null) {
			$this->libraryImageModel->deleteImage($image);
		}

		$this->redirect('default', ['id' => $libraryFolderId]);
	}

	public function handleDownloadOriginal(int $imageId): void
	{
		$libraryImage = $this->orm->libraryImage->getById($imageId);
		if ($libraryImage !== null && $this->imageStorage->existOriginal($libraryImage->fileNameWithoutExtension, $libraryImage->ext)) {
			$this->sendResponse(new FileResponse(WWW_DIR . $libraryImage->url));
		} else {
			$this->presenter->redirect('this');
		}
	}

	public function handleUpload(int $id, ?FileUpload $file = null): void
	{
		if ($file === null) {
			if (!empty($_FILES['file'])) {
				$file = new FileUpload($_FILES['file']);
			}
		}

		if ($file->isImage()) {
			$this->imageStorage->detectRotation($file);
		}

		$image = $this->libraryImageModel->addFromFileUpload($file, $id);
		$this->sendJson(['id' => $image->id]);
	}


	public function handleRotation(int $imageId, string $direction = 'left'): void
	{
		$libraryImage = $this->libraryImageRepository->getById($imageId);
		if ($libraryImage !== null && $this->imageStorage->existOriginal($libraryImage->fileNameWithoutExtension, $libraryImage->ext)) {



			if ($direction === 'left') {
				$this->imageStorage->rotate($libraryImage, 90);
			} else {
				$this->imageStorage->rotate($libraryImage, -90);
			}
			$libraryImage->timeOfChange = new DateTimeImmutable();
			$this->libraryImageRepository->persistAndFlush($libraryImage);
		}
		$this->presenter->redirect('this');
	}

	public function handleMoveImage(int $imageId, int $position): void
	{
		$libraryImage = $this->libraryImageRepository->getByIdChecked($imageId);
		assert($libraryImage instanceof LibraryImage);
		$libraryImage->sort = $position;
		$this->libraryImageRepository->persistAndFlush($libraryImage);
		if ($libraryImage->library !== null) {
			$this->object = $libraryImage->library;
			$this->libraryImageRepository->fixSortForDirectory($libraryImage->library);
		}
	}


	public function handleMoveImageToDirectory(int $imageId, int $id): void
	{
		$libraryImage = $this->libraryImageRepository->getByIdChecked($imageId);
		$libraryTree = $this->libraryTreeRepository->getByIdChecked($id);
		assert($libraryImage instanceof LibraryImage);
		assert($libraryTree instanceof LibraryTree);
		$libraryImage->sort = 0;
		$libraryImage->library = $libraryTree;
	}


	protected function createComponentLibraryFilter(): LibraryFilter
	{
		return $this->libraryFilterFactory->create();
	}

	protected function createComponentLibraryForm(): LibraryForm
	{
		return $this->libraryFormFactory->create($this->object);
	}
	protected function createComponentImageDetailForm(): ImageDetailForm
	{
		return $this->imageDetailFormFactory->create($this->libraryImage);
	}


	private function getNodeTreeStructure(): CommonTreeStructure
	{
		return new CommonTreeStructure(
			$this->libraryTreeRepository->findBy(['parent' => null])->orderBy('sort')->fetchAll(),
			$this->object->id,
		);
	}


	private function getTreeLink(int $id): string
	{
		return $this->link('default', ['id' => $id]);
	}


	private function moveNodeInTree(int $movedNodeId, int $targetNodeId, string $action): void
	{
		$movedNode = $this->libraryTreeRepository->getByIdChecked($movedNodeId);
		$targetNode = $this->libraryTreeRepository->getByIdChecked($targetNodeId);

		if (!in_array($action, ['after', 'before', 'inside', 'last'])) {
			throw new \LogicException('Unknown move action.');
		}

		$this->treeMovingModel->move($movedNode, $targetNode, $action);
		$this->redirect('default', ['id' => $movedNode->id]);
	}

	private function createNodeInTree(int $parentId, string $name): void
	{
		$parent = $this->libraryTreeRepository->getByIdChecked($parentId);
		$newLibraryTree = $this->libraryTreeModel->createNew($parent, $name, $this->userEntity);

		$this->redirect('default', ['id' => $newLibraryTree->id]);
	}


	protected function createComponentTree(): Tree
	{
		return $this->treeFactory->create(
			$this->getNodeTreeStructure(...),
			$this->moveNodeInTree(...),
			$this->createNodeInTree(...),
			$this->getTreeLink(...),
			(bool) $this->thickbox
		);
	}


	private function detectFirstPageEnforcer(): void
	{
		$forcedPage = $this->getParameter('forcedPage', null);
		if ($forcedPage !== null) {
			$this->forcedPage = (int)$forcedPage;
		}
	}

}
