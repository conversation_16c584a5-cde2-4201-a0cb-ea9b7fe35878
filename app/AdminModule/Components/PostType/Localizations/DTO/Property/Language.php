<?php declare(strict_types = 1);

namespace App\AdminModule\Components\PostType\Localizations\DTO\Property;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Core\Model\LocalizationEntity;
use Nextras\Orm\Entity\IEntity;

class Language
{

	private function __construct(
		public readonly bool $showPlus,
		public readonly string $name,
		public readonly int $mutationId,
		public readonly ?int $localizationId = null,
	)
	{
	}


	public static function create(
		Mutation $mutation,
		?LocalizationEntity $localizationEntity = null,
	): Language
	{

		assert($localizationEntity instanceof LocalizationEntity && $localizationEntity instanceof IEntity || $localizationEntity === null);


		return new self(
			showPlus: ($localizationEntity === null),
			name: $mutation->langCode,
			mutationId: $mutation->id,
			localizationId: ($localizationEntity !== null) ? $localizationEntity->getId() : null,
		);
	}

}
