{var $props = [
	id: $props['id'],
	title: $props['title'] ?? '',
	classes: $props['classes'] ?? [],
	data: $props['data'] ?? [],
]}

{var $classes = implode(' ', array_merge([
	'b-overlay',
	], $props['classes']))
}

<div
	id="overlay-{$props['id']}"
	class="{$classes}"
	{foreach $props['data'] as $key=>$value}
		data-{$key}="{$value|noescape}"
	{/foreach}
>
	<div class="b-overlay__bg" data-controller="Toggle" data-action="click->Toggle#changeClass" data-toggle-target-value="#overlay-{$props['id']}" data-toggle-target-class-value="is-visible"></div>
	<div class="b-overlay__inner">
		<div class="b-overlay__header">
			{include $templates.'/part/box/header.latte',
				props: [
					title: $props['title'],
					variant: 'overlay',
					hrefClose: '#overlay-'.$props['id'],
				]
			}
		</div>
		<div class="b-overlay__content scroll">
			<div class="row-main">
				{block content}{/block}
			</div>
		</div>
	</div>
</div>
