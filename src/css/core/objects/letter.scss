@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.letter {
	font-size: 1.5rem;
	line-height: calc(20 / 15);
	letter-spacing: -0.033em;
	&::first-letter {
		float: left;
		margin: -0.3rem 0.3rem -0.8rem -0.1rem;
		color: variables.$color-secondary;
		font-family: variables.$font-secondary;
		font-weight: bold;
		font-size: 3.8rem;
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.8rem;
		line-height: calc(26 / 18);
		&::first-letter {
			margin: -1.4rem 0.6rem -1.5rem 0;
			font-size: 5.6rem;
		}
	}
}
