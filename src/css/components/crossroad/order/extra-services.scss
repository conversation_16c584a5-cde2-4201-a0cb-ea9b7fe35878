@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-extra-services {
	&__title {
		margin: 0 0 1.2rem;
	}
	&__product {
		margin: 0 0 1.2rem;
		font-family: variables.$font-secondary;
		font-weight: bold;
		font-size: 1.3rem;
		a {
			--color-link: #{variables.$color-text};
			--color-hover: #{variables.$color-primary};
			text-decoration: none;
		}
	}
	&__items {
		@extend %reset-ul;
		display: grid;
		grid-template-columns: 1fr auto;
		font-size: 1.3rem;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		display: grid;
		grid-template-columns: subgrid;
		grid-column: auto / span 2;
		gap: 0.4rem 1.2rem;
		align-items: center;
		padding: 1rem;
		border-radius: variables.$border-radius-md;
	}
	&__check {
		position: absolute;
		top: -0.6rem;
		left: -0.6rem;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 2rem;
		border-radius: 50%;
		background: variables.$color-status-valid;
		color: variables.$color-white;
		aspect-ratio: 1/1;
		.icon-svg {
			width: 0.95rem;
		}
	}
	&__info {
		flex: 1;
		color: variables.$color-help;
	}
	&__name {
		margin: 0 0 0.3rem;
		font-weight: bold;
	}
	&__tooltip {
		top: -0.1rem;
		margin-left: 0.2rem;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover: var(--color-link);
	}
	&__price {
		margin: 0;
		color: variables.$color-black;
		font-weight: bold;
		font-size: 1.5rem;
		text-align: right;
	}
	&__btn {
		margin: 0;
		.btn {
			--btn-padding: 0.4rem 1.2rem 0.2rem;
			--btn-icon-size: 1.5rem;
			--btn-gap: 0.4rem;
			--btn-h: 3.4rem;
			--btn-fs: 1.4rem;
		}
	}

	// STATES
	&__item.is-active {
		border: 0.2rem solid variables.$color-status-valid;
	}

	// MQ
	@media (config.$lg-down) {
		&__product {
			a {
				display: flex;
				gap: 1rem;
				align-items: center;
			}
			.img {
				flex: 0 0 auto;
				width: 7.2rem;
			}
		}
	}
	@media (config.$md-down) {
		&__info {
			grid-row: 1 / span 2;
		}
		&__price {
			align-self: flex-end;
		}
		&__btn {
			align-self: flex-start;
		}
	}
	@media (config.$md-up) {
		&__title {
			margin: 0 0 2.4rem;
		}
		&__product {
			font-size: 1.5rem;
			line-height: 1.5;
		}
		&__items {
			grid-template-columns: 1fr auto auto;
			flex: 1;
			font-size: 1.4rem;
		}
		&__item {
			grid-column: auto / span 3;
			gap: 2rem;
			padding: 2rem 3.2rem;
			border-radius: variables.$border-radius-lg;
		}
		&__check {
			top: 0.7rem;
			left: 0.7rem;
		}
		&__name {
			font-size: 1.5rem;
		}
		&__btn {
			.btn {
				--btn-padding: 0.9rem 2rem 0.7rem;
				--btn-icon-size: 1.5rem;
				--btn-gap: 0.4rem;
				--btn-fs: 1.4rem;
				--btn-h: 4.4rem;
			}
		}
	}
	@media (config.$lg-up) {
		&__wrap {
			display: flex;
			gap: 2.4rem;
			align-items: center;
		}
		&__product {
			flex: 0 0 auto;
			width: 22rem;
			margin: 0;
			text-align: center;
			.img {
				margin: 0 0 0.8rem;
			}
		}
	}
	@media (config.$xl-up) {
		&__product {
			width: 29rem;
			a {
				display: block;
				padding: 2.4rem;
			}
		}
	}
}
