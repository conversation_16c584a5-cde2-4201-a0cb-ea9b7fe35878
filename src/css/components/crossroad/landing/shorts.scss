@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-shorts {
	&__title {
		margin: 0 0 0.8rem;
	}
	&__carousel {
		--arrow-position: 0rem;
		overflow: visible;
	}
	&__cell {
		width: max(calc(21rem + var(--grid-x-spacing)), calc(100% / 6));
	}
	&__more {
		margin: 0.8rem 0 0;
	}

	// MQ
	@media (config.$md-up) {
		&__title {
			margin: 0 0 2rem;
		}
		&__carousel {
			--arrow-position: calc((var(--vw) - 100%) / -2 + 2rem);
		}
		&__more {
			margin: 2rem 0 0;
			text-align: center;
		}
	}
}
