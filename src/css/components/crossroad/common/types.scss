@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-types {
	padding: 3.2rem 0;
	border: 0.1rem solid variables.$color-tile-light;
	border-width: 0.1rem 0;
	&__grid {
		--grid-x-spacing: 2rem;
		--grid-y-spacing: 3.2rem;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.4rem;
		flex-direction: column;
	}
	&__item {
		@extend %reset-ul-li;
		display: flex;
		gap: 1.2rem;
		flex-direction: column;
		padding: 1.2rem 1.6rem;
		border-radius: variables.$border-radius-md;
		background: variables.$color-bg;
		color: variables.$color-help;
		font-size: 1.3rem;
	}
	&__head {
		position: relative;
		margin: 0 0 1.6rem;
		padding-right: 5.2rem;
		font-size: 1.5rem;
	}
	&__title {
		margin: 0 0 0.8rem;
	}
	&__icon {
		position: absolute;
		top: 0;
		right: 0;
		width: 4rem;
		color: variables.$color-primary;
	}
	&__name {
		display: block;
		color: variables.$color-primary;
	}
	&__btn {
		flex: 0 0 auto;
	}

	// MQ
	@media (config.$xs-up) {
		&__item {
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
		}
	}
	@media (config.$lg-up) {
		padding: 8rem 0;
		&__grid {
			--grid-x-spacing: 4rem;
		}
		&__head {
			margin: 0 0 2.4rem;
			padding-right: 10.4rem;
			font-size: 1.7rem;
		}
		&__title {
			margin: 0 0 1.2rem;
		}
		&__icon {
			right: 2rem;
			width: 6rem;
		}
		&__item {
			gap: 4.5rem;
			padding: 1.7rem 2rem 1.7rem 2.8rem;
			border-radius: variables.$border-radius-lg;
			font-size: 1.4rem;
		}
	}
	@media (config.$xl-up) {
		padding: 8rem;
		border-width: 0.1rem;
		border-radius: variables.$border-radius-xl;
	}
}
