@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-btns {
	display: flex;
	gap: 1.6rem 2rem;
	flex-direction: column-reverse;
	flex-wrap: wrap;
	align-items: center;
	font-size: 1.5rem;
	.item-icon {
		--icon-size: 1.4rem;
		--gap: 0.8rem;
	}

	// MQ
	@media (config.$md-down) {
		&__agree {
			order: 1;
			text-align: center;
		}
	}
	@media (config.$sm-up) {
		&:not(:has(&__agree)) {
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: flex-end;
			&:has(> *:nth-child(2)) > *:nth-child(1) {
				margin-right: auto;
			}
		}
	}
	@media (config.$md-up) {
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: flex-end;
		&:has(> *:nth-child(2)) > *:nth-child(1) {
			margin-right: auto;
		}
		&__agree {
			text-align: right;
		}
	}
}
