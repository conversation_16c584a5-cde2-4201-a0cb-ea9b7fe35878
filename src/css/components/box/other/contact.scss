@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-contact {
	position: relative;
	margin: 0 0 2rem;
	border-radius: variables.$border-radius-xl;
	overflow: hidden;
	&::before,
	&::after {
		content: '';
		position: absolute;
		background: url(variables.$img-path + 'illust/dragonscale.webp');
		background-repeat: no-repeat;
		background-size: cover;
	}
	&::before {
		bottom: 0;
		left: 0;
		width: 24rem;
		height: 12rem;
	}
	&::after {
		top: 0;
		right: 0;
		width: 8.8rem;
		height: 10.7rem;
	}
	&__inner {
		position: relative;
		z-index: 1;
		padding: 2rem;
		background: rgba(variables.$color-bg, 0.85);
		backdrop-filter: blur(10rem);
	}
	&__title {
		margin: 0 0 1.2rem;
	}
	&__person {
		flex: 0 0 auto;
		width: 8rem;
	}
	&__phone {
		position: relative;
		display: inline-flex;
		gap: 1.2rem;
		align-items: center;
		margin: 0 0 2.5rem;
		color: variables.$color-help;
		font-size: 1.5rem;
		text-align: left;
		&::before {
			content: '';
			position: absolute;
			right: 0;
			bottom: -1.2rem;
			left: 0;
			height: 0.1rem;
			background: variables.$color-tile;
		}
	}
	&__icon {
		display: flex;
		flex: 0 0 auto;
		justify-content: center;
		align-items: center;
		width: 5rem;
		border-radius: 50%;
		background: variables.$color-white;
		color: variables.$color-primary;
		aspect-ratio: 1/1;
		.icon-svg {
			width: 3rem;
		}
	}
	&__link {
		--color-link: #{variables.$color-primary};
		--color-hover: var(--color-link);
		--color-link-decoration: transparent;
		display: block;
		font-weight: bold;
		font-size: 1.6rem;
	}
	&__mail {
		margin: 0;

		.item-icon {
			--icon-size: 2.4rem;
			--icon-offset: 0.05em;
			--icon-color: #{variables.$color-icon-minor};
		}
	}

	// MQ
	@media (config.$lg-down) {
		text-align: center;
		&__person {
			margin: 0 auto 1.2rem;
		}
		&__phone {
			line-height: 1.4;
		}
	}
	@media (config.$lg-up) {
		margin: 0 0 3.2rem;
		&::before {
			top: 0;
			width: 41.1rem;
			height: 20.6rem;
		}
		&::after {
			width: 12.3rem;
			height: 15rem;
		}
		&__inner {
			display: flex;
			gap: 3rem;
			justify-content: center;
			align-items: center;
			padding: 6rem 3rem;
		}
		&__title {
			display: flex;
			gap: 2rem;
			align-items: center;
			margin: 0;
		}
		&__phone {
			margin: 0;
			&::before {
				top: 0.8rem;
				right: -1.5rem;
				bottom: 0.8rem;
				left: auto;
				width: 0.1rem;
				height: auto;
			}
		}
		&__icon {
			width: 5.4rem;
		}
		&__link {
			font-size: 1.8rem;
		}
	}
	@media (config.$xl-up) {
		&__inner {
			gap: 6rem;
		}
		&__phone::before {
			right: -3rem;
		}
	}
}
