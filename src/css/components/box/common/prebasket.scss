@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-prebasket {
	.row-main {
		--row-main-gutter: 2rem;
	}
	&__box {
		margin: 0 0 0.4rem;
		border-radius: variables.$border-radius-sm;
		background: variables.$color-white;
		overflow: hidden;
	}
	&__inner {
		display: flex;
		align-items: center;
		& > * {
			max-width: 100%;
		}
	}
	&__products {
		--spacing: 2rem;
		--cell-size: 18.5rem;
		width: 100%;
		.b-product__extra {
			display: none;
		}
	}
	&__text {
		display: flex;
		gap: 1rem;
		align-items: center;
		color: variables.$color-green;
	}
	&__check {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 3rem;
		height: 3rem;
		border: 0.2rem solid variables.$color-green;
		border-radius: 50%;
		color: variables.$color-green;
		.icon-svg {
			width: 1.4rem;
		}
	}
	&__book {
		display: flex;
		gap: 1.2rem;
		align-items: center;
		font-size: 1.3rem;
	}
	&__img {
		flex: 0 0 auto;
		width: 4rem;
		height: 6.4rem;
	}
	&__price {
		font-size: 1.2rem;
	}
	&__delivery {
		width: 100%;
		max-width: 35rem;
	}

	// MODIF
	&__inner--product {
		gap: 1.8rem 3.5rem;
		flex-wrap: wrap;
		padding: 2.4rem 0 1.5rem;
	}
	&__inner--tools {
		gap: 1rem 3rem;
		flex-direction: column;
		padding: 1.6rem 0;
	}
	&__inner--products {
		padding: 2.4rem 0 4rem;
	}

	// MQ
	@media (config.$md-down) {
		hr {
			margin: 0 calc(var(--row-main-gutter) * -1);
		}
		&__inner--tools > * {
			order: 2;
			width: 100%;
			.btn {
				width: 100%;
			}
		}
		&__delivery {
			order: 1;
			.b-delivery__text {
				justify-content: flex-start;
			}
		}
	}
	@media (config.$md-up) {
		&__box {
			margin: 0 0 1.5rem;
		}
		&__inner {
			justify-content: center;
		}
		&__text {
			gap: 1.2rem;
		}
		&__book {
			gap: 1.6rem;
			font-size: 1.4rem;
		}
		&__img {
			width: 4.7rem;
			height: 7.6rem;
		}
		&__price {
			font-size: 1.3rem;
		}
		&__delivery {
			flex: 0 1 40%;
		}

		// MODIF
		&__inner--product {
			padding: 4rem 0;
		}
		&__inner--tools {
			gap: 3rem;
			flex-direction: row;
			justify-content: space-between;
			padding: 2.4rem 0;
		}
		&__inner--products {
			padding: 6rem 0 7rem;
		}
	}
	@media (config.$lg-up) {
		.row-main {
			--row-main-gutter: 7rem;
		}
	}
	@media (config.$xxxl-up) {
		&__products .section__carousel {
			--arrow-position: 0;
		}
	}
}
