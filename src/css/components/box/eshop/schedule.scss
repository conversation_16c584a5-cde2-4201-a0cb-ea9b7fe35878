@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-schedule {
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 1.6rem;
		flex-direction: column;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__head {
		margin: 0 0 1.2rem;
	}
	&__name {
		display: inline;
	}
	&__flags {
		position: relative;
		top: -0.1rem;
		margin-left: 0.2rem;
		.flag {
			--flag-h: 2.2rem;
			--flag-fs: 1.1rem;
			--flag-padding: 0.1rem 0.8rem;
		}
	}
	&__content {
		ul {
			@extend %reset-ul;
			position: relative;
			display: flex;
			gap: 0.3rem;
			flex-direction: column;
			&::before {
				content: '';
				position: absolute;
				top: 0;
				bottom: 0;
				left: 0.8rem;
				width: 0.15rem;
				background: variables.$color-primary;
			}
		}
		li {
			@extend %reset-ul-li;
			position: relative;
			display: flex;
			align-items: center;
			min-height: 3rem;
			padding-left: 2.3rem;
			&::before {
				content: '';
				position: absolute;
				top: 1.1rem;
				left: 0.5rem;
				width: 0.8rem;
				height: 0.8rem;
				border: 0.15rem solid variables.$color-primary;
				border-radius: 50%;
				background: variables.$color-white;
			}
		}
	}

	// MQ
	@media (config.$md-up) {
		&__head {
			margin: 0 0 1.6rem;
		}
		&__name {
			margin: 0 0 1.6rem;
		}
		&__flags {
			top: -0.3rem;
			margin-left: 0.6rem;
			.flag {
				--flag-h: 2.6rem;
				--flag-fs: 1.4rem;
				--flag-padding: 0.2rem 0.9rem;
			}
		}
		&__list {
			gap: 2.4rem;
		}
		&__content {
			ul {
				&::before {
					left: 1.4rem;
				}
			}
			li {
				min-height: 3.4rem;
				padding-left: 3.4rem;
				&::before {
					top: 1.3rem;
					left: 1.05rem;
				}
			}
		}
	}
}
