import $ from 'jquery';

export const hasSessionStorage = () => typeof sessionStorage !== 'undefined';

export const handleSnippetResponse = (response) => {
	if (!response.snippets) {
		console.warn('No snippets in response');
		return;
	}

	Object.keys(response.snippets).forEach((id) => {
		var $target = $(`#${id}`);
		if ($target.attr('data-append')) {
			//append
			$target.append(response.snippets[id]);
		} else {
			//replace
			$target.html(response.snippets[id]);
		}
	});
};

export const getPreviousSiblings = (el) => {
	const siblings = [];
	let prevSibling = el.previousElementSibling;
	while (prevSibling) {
		siblings.push(prevSibling);
		prevSibling = prevSibling.previousElementSibling;
	}

	return siblings;
};

export const getNextSiblings = (el) => {
	const siblings = [];
	let nextSibling = el.nextElementSibling;
	while (nextSibling) {
		siblings.push(nextSibling);
		nextSibling = nextSibling.nextElementSibling;
	}

	return siblings;
};

export const arrayMove = (arr, fromIndex, toIndex) => {
	const newArr = [...arr];
	const element = newArr[fromIndex];
	newArr.splice(fromIndex, 1);
	newArr.splice(toIndex, 0, element);

	return newArr;
};
