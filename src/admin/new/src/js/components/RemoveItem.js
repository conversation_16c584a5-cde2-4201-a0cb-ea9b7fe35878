import $ from 'jquery';
import { Controller } from 'stimulus';

export default class RemoveItem extends Controller {
	static targets = ['item'];
	static values = {
		animation: String,
	};

	remove() {
		if (this.animationValue == 'fade') {
			$(this.itemTarget).fadeOut(() => {
				$(this.itemTarget).remove();
			});
		} else {
			$(this.itemTarget).slideUp(() => {
				$(this.itemTarget).remove();
			});
		}
	}
}
