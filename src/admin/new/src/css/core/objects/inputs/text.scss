.inp-text {
	display: block;
	width: 100%;
	padding: 8px 10px;
	border: 1px solid transparent;
	border-radius: $radius;
	background-color: $colorWhite;
	color: $colorText;
	outline: none;
	font-size: $fontSize;
	line-height: 20px;
	transition: background-color $t, border-color $t;
	appearance: none;
	&::placeholder {
		color: rgba($colorText, 0.5);
		opacity: 1;
	}
	&__holder {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		border: 1px solid $colorBd;
		border-radius: $radius;
		transition: background-color $t, border-color $t;
		pointer-events: none;
		box-shadow: $shadow;
	}

	// VARIANTs
	textarea#{&} {
		height: auto;
	}

	.inp--h1 & {
		padding: 0 0 8px;
		border-width: 0 0 1px;
		border-bottom-color: transparent;
		font-weight: bold;
		font-size: 28px;
		line-height: 28px;
		&__holder {
			border-width: 0 0 1px;
		}
	}

	// STATEs
	&:disabled {
		background-color: $colorBg;
	}
	&:focus + &__holder {
		outline-color: $colorPrimary;
	}
	.has-error & + &__holder {
		outline-color: $colorRed;
	}
	&:focus {
		&::placeholder {
			color: rgba($colorBlack, 0.25);
		}
	}

	// .b-toggle &__holder {
	// 	&::before {
	// 		content: '';
	// 		position: absolute;
	// 		left: -21px;
	// 		top: -30px;
	// 		bottom: -5px;
	// 		width: 2px;
	// 		background: $colorPrimary;
	// 		opacity: 0;
	// 		transition: opacity $t;
	// 	}
	// }
	// .b-toggle &:focus + &__holder {
	// 	&::before {
	// 		opacity: 1;
	// 	}
	// }
}
