{% embed '@components/box/overlay.twig' with {
	props: {
		id: 'overlay-newItemMarker',
		size: 'md',
		title: 'Editace / Přidání varianty',
		data: {
			'controller': 'ProductVariantEdit',
			'ProductVariantEdit-id-value': 'newItemMarker',
		},
		classes: ['is-visible'],
	}
} %}{% block content %}
	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Publikace v modifikacích',
			id: 'overlay-content-variant-public',
			open: true,
			tags: [
				{
					text: 'Povinné'
				},
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="inp js-lang js-lang--cz">
			{% include '@components/core/checkbox.twig' with {
				props: {
					id: '[newItemMarker]-published-cz',
					label: '<span class="grid-inline"><span class="tag">CZ</span> <span>Aktivováno</span></span>',
					dataInp: {
						'action': 'change->ProductVariantEdit#edit',
						'ProductVariantEdit-target': 'lang',
						'lang': 'CZ',
					}
				}
			} %}
		</div>
		<div class="inp js-lang js-lang--en">
			{% include '@components/core/checkbox.twig' with {
				props: {
					id: '[newItemMarker]-published-en',
					label: '<span class="grid-inline"><span class="tag">EN</span> <span>Aktivováno</span></span>',
					dataInp: {
						'action': 'change->ProductVariantEdit#edit',
						'ProductVariantEdit-target': 'lang',
						'lang': 'EN',
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Název varianty',
			id: 'overlay-content-variant-name',
			open: true,
			tags: [
				{
					text: 'Povinné'
				},
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: '[variants][newItemMarker][name]',
					label: 'CZ',
					classesLabel: ['tag'],
					dataInp: {
						'action': 'input->ProductVariantEdit#edit',
						'ProductVariantEdit-target': 'name',
						'lang': 'CZ',
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: '[variants][newItemMarker][name]',
					label: 'EN',
					classesLabel: ['tag'],
					dataInp: {
						'action': 'input->ProductVariantEdit#edit',
						'ProductVariantEdit-target': 'name',
						'lang': 'EN',
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Cena s DPH',
			id: 'overlay-content-variant-price',
			open: true,
			tags: [
				{
					text: 'Povinné'
				},
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: '[newItemMarker]-price-cz',
					label: 'CZ',
					classesLabel: ['tag'],
					prefix: 'Kč',
					dataInp: {
						'ProductVariantEdit-target': 'price',
						'action': 'input->ProductVariantEdit#edit',
						'lang': 'CZ',
						'currency': 'Kč',
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: '[newItemMarker]-price-en',
					label: 'EN',
					classesLabel: ['tag'],
					prefix: '€',
					dataInp: {
						'ProductVariantEdit-target': 'price',
						'action': 'input->ProductVariantEdit#edit',
						'lang': 'EN',
						'currency': '€',
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Kusy',
			id: 'overlay-content-variant-amount',
			open: true,
			tags: [
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: '[newItemMarker]-amount-cz',
					label: 'CZ',
					classesLabel: ['tag'],
					prefix: 'ks',
					dataInp: {
						'ProductVariantEdit-target': 'amount',
						'action': 'input->ProductVariantEdit#edit',
						'lang': 'CZ',
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: '[newItemMarker]-amount-en',
					label: 'EN',
					classesLabel: ['tag'],
					prefix: 'ks',
					dataInp: {
						'ProductVariantEdit-target': 'amount',
						'action': 'input->ProductVariantEdit#edit',
						'lang': 'EN',
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Definice varianty',
			id: 'overlay-content-variant-defs',
			open: true,
			tags: [
				{
					text: 'Povinné'
				}
			]
		}
	} %}{% block content %}
		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12">
				{% include '@components/core/inp.twig' with {
					props: {
						id: '[newItemMarker]-color',
						label: 'Barva',
						classesLabel: ['title'],
						type: 'select',
						options: [
							{
								text: 'Vyberte šablonu'
							},
							{
								text: 'Defaultní'
							},
							{
								text: 'O nás'
							},
							{
								text: 'Kontakt'
							}
						]
					}
				} %}
			</div>
			<div class="grid__cell size--6-12">
				{% include '@components/core/inp.twig' with {
					props: {
						id: '[newItemMarker]-size',
						label: 'Velikost',
						classesLabel: ['title'],
						type: 'select',
						options: [
							{
								text: 'Vyberte šablonu'
							},
							{
								text: 'Defaultní'
							},
							{
								text: 'O nás'
							},
							{
								text: 'Kontakt'
							}
						]
					}
				} %}
			</div>
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Akční produkt',
			id: 'overlay-content-variant-action',
			tags: [
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		{% set content %}
			<div class="u-mb-sm">
				<div class="grid grid--y-0">
					<div class="grid__cell size--6-12">
						{% include '@components/core/inp.twig' with {
							props: {
								id: '[newItemMarker]-discount-amount-cz',
								label: 'Velikost slevy',
								classesLabel: ['title'],
								prefix: 'Kč',
								type: 'number'
							}
						} %}
					</div>
					<div class="grid__cell size--6-12">
						{% include '@components/core/inp.twig' with {
							props: {
								id: '[newItemMarker]-discount-percentage-cz',
								label: 'Použijte jednu z možností % nebo pevná částka',
								classesLabel: ['u-font-regular', 'u-text-right', 'u-font-sm'],
								prefix: '%',
								type: 'number'
							}
						} %}
					</div>
					<div class="grid__cell size--6-12">
						{% include '@components/core/inp.twig' with {
							props: {
								id: '[newItemMarker]-discount-from-cz',
								label: 'Platnost od',
								classesLabel: ['title'],
								type: 'datetime-local',
								classes: ['']
							}
						} %}
					</div>
					<div class="grid__cell size--6-12">
						{% include '@components/core/inp.twig' with {
							props: {
								id: '[newItemMarker]-discount-to-cz',
								label: 'do',
								classesLabel: ['title'],
								type: 'datetime-local',
								classes: ['']
							}
						} %}
					</div>
				</div>
			</div>
		{% endset %}

		<div class="js-lang js-lang--cz">
			<div class="u-mb-xxs tag">
				CZ
			</div>
			{{ content }}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/checkbox.twig' with {
				props: {
					label: '<span class="grid-inline"><span class="tag">EN</span> <span>přebírá nastavení z výchozí verze</span>',
					checked: true,
					attrsInp: [
						'data-controller="ToggleCheckbox"',
						'data-action="ToggleCheckbox#changeClass"',
						'data-togglecheckbox-target-value="#checkbox-variants"',
						'data-togglecheckbox-target-class-value="is-open"'
					],
				}
			} %}
			<div id="checkbox-variants" class="js-toggle-checkbox__content u-mt--xs">
				{{ content }}
			</div>
		</div>
	{% endblock %}{% endembed %}

	{% include '@components/core/inp.twig' with {
		props: {
			id: '[newItemMarker]-ean',
			label: 'EAN',
		}
	} %}

	{% include '@components/core/inp.twig' with {
		props: {
			id: '[newItemMarker]-code',
			label: 'Kód produktu',
		}
	} %}

	<p>
		TODO: Obrázek(y) pro variantu?
	</p>
{% endblock %}{% endembed %}
