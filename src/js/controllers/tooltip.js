import { Controller } from '@hotwired/stimulus';
import naja from 'naja';

export const create = () => {
	return class extends Controller {
		static targets = ['content', 'btn'];
		static values = {
			placement: { type: String, default: 'bottom' },
			settings: { type: Object },
		};

		async connect() {
			this.tippy = (await import(/* webpackPrefetch: true */ 'tippy.js')).default;
			this.tooltip = this.tippy(this.btnTarget, {
				content: this.contentTarget.innerHTML,
				placement: this.placementValue,
				allowHTML: true,
				interactive: true,
				appendTo: document.body,
				offset: ({ placement }) => {
					// Determine offset dynamically based on final placement
					return placement.includes('start') ? [-37, 15] : placement.includes('end') ? [37, 15] : [0, 15];
				},
				...this.settingsValue,
				onShow(instance) {
					instance.popper.querySelector('.js-tooltip-close')?.addEventListener('click', () => {
						instance.hide();
					});

					naja.uiHandler.bindUI(instance.popper);
				},
				onHide(instance) {
					instance.popper.querySelector('.js-tooltip-close')?.removeEventListener('click', () => {
						instance.hide();
					});
				},
			});
		}

		disconnect() {
			if (this.tooltip) this.tooltip.destroy();
		}
	};
};
